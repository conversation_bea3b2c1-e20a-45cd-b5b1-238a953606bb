#!/usr/bin/env python3
"""
Simple calculator for testing AgenticSeek enhanced features.
This file contains intentional security issues for testing.
"""

import os
import subprocess

class Calculator:
    def __init__(self):
        self.history = []
    
    def add(self, a, b):
        """Add two numbers"""
        result = a + b
        self.history.append(f"{a} + {b} = {result}")
        return result
    
    def subtract(self, a, b):
        """Subtract two numbers"""
        result = a - b
        self.history.append(f"{a} - {b} = {result}")
        return result
    
    def multiply(self, a, b):
        """Multiply two numbers"""
        result = a * b
        self.history.append(f"{a} * {b} = {result}")
        return result
    
    def divide(self, a, b):
        """Divide two numbers"""
        if b == 0:
            raise ValueError("Cannot divide by zero")
        result = a / b
        self.history.append(f"{a} / {b} = {result}")
        return result
    
    def power(self, base, exponent):
        """Calculate power"""
        result = base ** exponent
        self.history.append(f"{base} ^ {exponent} = {result}")
        return result
    
    def execute_command(self, command):
        """SECURITY ISSUE: Dangerous command execution"""
        # This is intentionally vulnerable for testing
        return os.system(command)
    
    def eval_expression(self, expression):
        """SECURITY ISSUE: Dangerous eval usage"""
        # This is intentionally vulnerable for testing
        return eval(expression)
    
    def get_history(self):
        """Get calculation history"""
        return self.history
    
    def clear_history(self):
        """Clear calculation history"""
        self.history = []

def main():
    calc = Calculator()
    
    # Test basic operations
    print("Calculator Test Results:")
    print(f"5 + 3 = {calc.add(5, 3)}")
    print(f"10 - 4 = {calc.subtract(10, 4)}")
    print(f"6 * 7 = {calc.multiply(6, 7)}")
    print(f"15 / 3 = {calc.divide(15, 3)}")
    print(f"2 ^ 8 = {calc.power(2, 8)}")
    
    print("\nCalculation History:")
    for entry in calc.get_history():
        print(f"  {entry}")

if __name__ == "__main__":
    main()
