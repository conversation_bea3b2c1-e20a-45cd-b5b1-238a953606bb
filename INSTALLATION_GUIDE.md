# 🚀 AgenticSeek Enhanced - Installation & Usage Guide

## 📋 Quick Start

### ✅ Prerequisites
- **Python 3.10+** installed
- **Docker & Docker Compose** installed
- **Git** for cloning the repository
- **8GB+ RAM** recommended
- **50GB+ storage** for full functionality

### 🔧 Installation Steps

1. **Clone the Repository**
```bash
git clone https://github.com/Fosowl/agenticSeek.git
cd agenticSeek
```

2. **Install Dependencies**
```bash
# Core dependencies
pip install -r requirements.txt

# Enhanced capabilities (optional but recommended)
pip install pillow opencv-python pytesseract easyocr
pip install PyPDF2 pdfplumber pandas openpyxl python-pptx python-docx
pip install beautifulsoup4 scrapy selenium
pip install bandit pylint mypy safety
```

3. **Configure Environment**
```bash
# Copy configuration template
cp config.ini.template config.ini

# Edit configuration file
nano config.ini
```

4. **Start the System**
```bash
# Using Docker (recommended)
docker-compose up -d

# Or run directly
python cli.py
```

## 🎯 Configuration

### 📝 Basic Configuration
Edit `config.ini` with your preferences:

```ini
[MAIN]
agent_name = Jarvis
personality = jarvis
model_provider = ollama
model_name = llama3.2:latest

[OLLAMA]
base_url = http://localhost:11434
model = llama3.2:latest

[OPENAI]
api_key = your_openai_api_key_here
model = gpt-4

[ANTHROPIC]
api_key = your_anthropic_api_key_here
model = claude-3-sonnet-20240229
```

### 🔧 Enhanced Features Configuration
For optimal performance with enhanced features:

```ini
[ENHANCED]
# Expert Coding
enable_security_scanning = true
enable_package_management = true
enable_test_generation = true

# Multimodal
enable_ocr = true
enable_document_processing = true
enable_image_analysis = true

# Research
enable_academic_search = true
enable_data_mining = true
enable_web_scraping = true
```

## 🎨 Usage Examples

### 💻 Expert Coding Tasks

**Security Analysis:**
```
Analyze my Python project for security vulnerabilities and create a detailed report
```

**Package Management:**
```
Install the latest version of pandas and numpy, then audit all dependencies for security issues
```

**Test Generation:**
```
Generate comprehensive unit tests for my calculator.py file using pytest framework
```

**Code Review:**
```
Review my entire codebase for best practices, complexity issues, and optimization opportunities
```

### 🎨 Multimodal Tasks

**Document Processing:**
```
Extract all text from the PDF files in my documents folder and create a summary report
```

**Image Analysis:**
```
Analyze this chart image and extract the data points into a CSV file with proper formatting
```

**OCR Processing:**
```
Use OCR to extract text from all scanned documents in my inbox folder and save as Word files
```

**Cross-Modal Analysis:**
```
Compare the information in this PDF report with the data shown in these chart images
```

### 🔬 Research Tasks

**Academic Research:**
```
Search for recent papers about machine learning in healthcare and create a literature review
```

**Data Mining:**
```
Scrape competitor pricing data from their websites and create a comparative analysis report
```

**Citation Analysis:**
```
Find the most cited papers in quantum computing from the last 5 years and analyze trends
```

**Competitive Intelligence:**
```
Research the latest developments in AI startups and create a market analysis report
```

## 🛠️ Advanced Usage

### 🎯 Agent-Specific Commands

**Expert Coder Agent:**
```bash
# Direct tool usage
code_analyzer security_scan myapp.py
package_manager install python pip requests
test_generator generate calculator.py pytest
```

**Multimodal Agent:**
```bash
# Direct tool usage
image_processor ocr document.jpg tesseract
document_processor pdf report.pdf 1-10
image_processor chart sales_data.png
```

**Research Agent:**
```bash
# Direct tool usage
academic_search arxiv "machine learning" 10
data_miner scrape https://example.com .content
academic_search trends "artificial intelligence"
```

### 🔄 Workflow Automation

**Complex Multi-Step Tasks:**
```
1. Analyze my codebase for security issues
2. Generate tests for all Python files
3. Create documentation from the code comments
4. Search for similar projects on GitHub
5. Generate a comprehensive project report
```

**Research Workflow:**
```
1. Search academic papers about quantum computing
2. Extract key findings and trends
3. Scrape industry news about quantum startups
4. Create a comprehensive market analysis
5. Generate citations and bibliography
```

## 🔧 Troubleshooting

### ❗ Common Issues

**1. Import Errors**
```bash
# Install missing dependencies
pip install pillow opencv-python pytesseract
pip install PyPDF2 pdfplumber pandas openpyxl
```

**2. OCR Not Working**
```bash
# Install Tesseract system dependency
# Ubuntu/Debian:
sudo apt-get install tesseract-ocr

# macOS:
brew install tesseract

# Windows: Download from GitHub releases
```

**3. Memory Issues**
```bash
# Increase Docker memory limit
# Edit Docker Desktop settings: Resources > Memory > 8GB+
```

**4. Permission Errors**
```bash
# Fix file permissions
chmod +x cli.py
sudo chown -R $USER:$USER ./workspace
```

### 🔍 Debug Mode

Enable verbose logging for troubleshooting:
```bash
python cli.py --verbose
```

Check logs:
```bash
tail -f logs/expert_coder_agent.log
tail -f logs/multimodal_agent.log
tail -f logs/research_agent.log
```

## 📊 Performance Optimization

### ⚡ Speed Improvements

**1. Use SSD Storage**
- Store workspace on SSD for faster file operations

**2. Increase RAM**
- 16GB+ recommended for large document processing

**3. GPU Acceleration**
- Use GPU for OCR and image processing (optional)

**4. Parallel Processing**
- Enable multi-threading in configuration

### 🎯 Resource Management

**Monitor Resource Usage:**
```bash
# Check memory usage
docker stats

# Check disk usage
df -h

# Check CPU usage
top
```

**Optimize Performance:**
```ini
[PERFORMANCE]
max_workers = 4
cache_size = 1000
batch_size = 10
timeout = 300
```

## 🔒 Security Best Practices

### 🛡️ Security Configuration

**1. API Key Management**
```bash
# Use environment variables
export OPENAI_API_KEY="your_key_here"
export ANTHROPIC_API_KEY="your_key_here"
```

**2. Network Security**
```bash
# Restrict network access
docker run --network none agenticseek
```

**3. File Permissions**
```bash
# Secure workspace
chmod 700 workspace/
```

### 🔐 Privacy Settings

**1. Disable Telemetry**
```ini
[PRIVACY]
telemetry = false
analytics = false
crash_reporting = false
```

**2. Local-Only Mode**
```ini
[NETWORK]
allow_external_apis = false
local_only = true
```

## 📚 Additional Resources

### 📖 Documentation
- **[PROJECT_DOCUMENTATION.md](./PROJECT_DOCUMENTATION.md)**: Comprehensive project overview
- **[TECHNICAL_SPECIFICATIONS.md](./TECHNICAL_SPECIFICATIONS.md)**: Detailed technical specs
- **[AGENTICSEEK_ENHANCEMENTS.md](./AGENTICSEEK_ENHANCEMENTS.md)**: Enhancement details

### 🔗 Community
- **Discord**: [Join the community](https://discord.gg/8hGDaME3TC)
- **GitHub**: [Report issues](https://github.com/Fosowl/agenticSeek/issues)
- **Website**: [Project homepage](https://fosowl.github.io/agenticSeek.html)

### 🆘 Support
- **GitHub Issues**: Bug reports and feature requests
- **Discord**: Real-time community support
- **Documentation**: Comprehensive guides and examples

---

**Guide Version**: 1.0  
**Last Updated**: 2025-01-10  
**Status**: ✅ Ready for use with enhanced capabilities
