import os
import sys
import subprocess
import json
import re
from typing import Dict, List, Optional, Tuple
from pathlib import Path

if __name__ == "__main__":
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from sources.tools.tools import Tools

class PackageManager(Tools):
    """
    Multi-language package manager tool for installing, updating, and managing dependencies.
    Supports Python (pip, poetry, conda), JavaScript (npm, yarn, pnpm), Java (maven, gradle),
    Go (go mod), Rust (cargo), Ruby (gem, bundle), P<PERSON> (composer), and C#/.NET (dotnet).
    """
    
    def __init__(self):
        super().__init__()
        self.tag = "package_manager"
        self.name = "Package Manager"
        self.description = "Multi-language package manager for dependency management"
        
        # Package manager configurations
        self.package_managers = {
            'python': {
                'pip': {'install': 'pip install', 'uninstall': 'pip uninstall', 'list': 'pip list', 'update': 'pip install --upgrade'},
                'poetry': {'install': 'poetry add', 'uninstall': 'poetry remove', 'list': 'poetry show', 'update': 'poetry update'},
                'conda': {'install': 'conda install', 'uninstall': 'conda remove', 'list': 'conda list', 'update': 'conda update'}
            },
            'javascript': {
                'npm': {'install': 'npm install', 'uninstall': 'npm uninstall', 'list': 'npm list', 'update': 'npm update'},
                'yarn': {'install': 'yarn add', 'uninstall': 'yarn remove', 'list': 'yarn list', 'update': 'yarn upgrade'},
                'pnpm': {'install': 'pnpm add', 'uninstall': 'pnpm remove', 'list': 'pnpm list', 'update': 'pnpm update'}
            },
            'java': {
                'maven': {'install': 'mvn dependency:resolve', 'list': 'mvn dependency:list', 'update': 'mvn versions:use-latest-versions'},
                'gradle': {'install': 'gradle build', 'list': 'gradle dependencies', 'update': 'gradle dependencyUpdates'}
            },
            'go': {
                'go': {'install': 'go get', 'uninstall': 'go mod edit -droprequire', 'list': 'go list -m all', 'update': 'go get -u', 'tidy': 'go mod tidy'}
            },
            'rust': {
                'cargo': {'install': 'cargo add', 'uninstall': 'cargo remove', 'list': 'cargo tree', 'update': 'cargo update'}
            },
            'ruby': {
                'gem': {'install': 'gem install', 'uninstall': 'gem uninstall', 'list': 'gem list', 'update': 'gem update'},
                'bundle': {'install': 'bundle add', 'uninstall': 'bundle remove', 'list': 'bundle list', 'update': 'bundle update'}
            },
            'php': {
                'composer': {'install': 'composer require', 'uninstall': 'composer remove', 'list': 'composer show', 'update': 'composer update'}
            },
            'csharp': {
                'dotnet': {'install': 'dotnet add package', 'uninstall': 'dotnet remove package', 'list': 'dotnet list package', 'update': 'dotnet add package --version'}
            }
        }
    
    def execute(self, blocks: List[str], safety: bool = False) -> str:
        """
        Execute package management commands.
        
        Commands:
        - install <language> <package_manager> <package_name> [version] - Install a package
        - uninstall <language> <package_manager> <package_name> - Uninstall a package
        - list <language> <package_manager> - List installed packages
        - update <language> <package_manager> [package_name] - Update packages
        - detect [directory] - Auto-detect project type and package manager
        - init <language> <package_manager> - Initialize new project
        - audit <language> <package_manager> - Security audit of dependencies
        """
        if not blocks:
            return self._get_help()
        
        command_line = blocks[0].strip()
        parts = command_line.split()
        
        if len(parts) < 1:
            return self._get_help()
        
        command = parts[0].lower()
        
        try:
            if command == "install":
                if len(parts) < 4:
                    return "Error: install requires <language> <package_manager> <package_name> [version]"
                language, pm, package = parts[1], parts[2], parts[3]
                version = parts[4] if len(parts) > 4 else None
                return self._install_package(language, pm, package, version)
            
            elif command == "uninstall":
                if len(parts) < 4:
                    return "Error: uninstall requires <language> <package_manager> <package_name>"
                language, pm, package = parts[1], parts[2], parts[3]
                return self._uninstall_package(language, pm, package)
            
            elif command == "list":
                if len(parts) < 3:
                    return "Error: list requires <language> <package_manager>"
                language, pm = parts[1], parts[2]
                return self._list_packages(language, pm)
            
            elif command == "update":
                if len(parts) < 3:
                    return "Error: update requires <language> <package_manager> [package_name]"
                language, pm = parts[1], parts[2]
                package = parts[3] if len(parts) > 3 else None
                return self._update_packages(language, pm, package)
            
            elif command == "detect":
                directory = parts[1] if len(parts) > 1 else "."
                return self._detect_project_type(directory)
            
            elif command == "init":
                if len(parts) < 3:
                    return "Error: init requires <language> <package_manager>"
                language, pm = parts[1], parts[2]
                return self._init_project(language, pm)
            
            elif command == "audit":
                if len(parts) < 3:
                    return "Error: audit requires <language> <package_manager>"
                language, pm = parts[1], parts[2]
                return self._audit_dependencies(language, pm)
            
            else:
                return f"Unknown command: {command}\n\n{self._get_help()}"
        
        except Exception as e:
            return f"Error during package management: {str(e)}"
    
    def _get_help(self) -> str:
        """Return help information for the package manager."""
        return """Package Manager Commands:

install <lang> <pm> <package> [version]  - Install a package
uninstall <lang> <pm> <package>          - Uninstall a package
list <lang> <pm>                         - List installed packages
update <lang> <pm> [package]             - Update packages
detect [directory]                       - Auto-detect project type
init <lang> <pm>                         - Initialize new project
audit <lang> <pm>                        - Security audit

Supported Languages & Package Managers:
- python: pip, poetry, conda
- javascript: npm, yarn, pnpm
- java: maven, gradle
- go: go
- rust: cargo
- ruby: gem, bundle
- php: composer
- csharp: dotnet

Examples:
```package_manager
install python pip requests
```

```package_manager
detect .
```

```package_manager
list javascript npm
```
"""
    
    def _install_package(self, language: str, pm: str, package: str, version: Optional[str] = None) -> str:
        """Install a package using the specified package manager."""
        if language not in self.package_managers:
            return f"Error: Unsupported language '{language}'"
        
        if pm not in self.package_managers[language]:
            return f"Error: Unsupported package manager '{pm}' for {language}"
        
        # Check if package manager is available
        if not self._check_pm_available(pm):
            return f"Error: Package manager '{pm}' is not installed or not in PATH"
        
        pm_config = self.package_managers[language][pm]
        install_cmd = pm_config['install']
        
        # Build the command
        if version:
            if pm == 'pip':
                cmd = f"{install_cmd} {package}=={version}"
            elif pm in ['npm', 'yarn']:
                cmd = f"{install_cmd} {package}@{version}"
            elif pm == 'cargo':
                cmd = f"{install_cmd} {package}@{version}"
            else:
                cmd = f"{install_cmd} {package} --version {version}"
        else:
            cmd = f"{install_cmd} {package}"
        
        # Execute the command
        try:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                return f"✅ Successfully installed {package}\n\nOutput:\n{result.stdout}"
            else:
                return f"❌ Failed to install {package}\n\nError:\n{result.stderr}"
        
        except subprocess.TimeoutExpired:
            return f"❌ Installation of {package} timed out (5 minutes)"
        except Exception as e:
            return f"❌ Error installing {package}: {str(e)}"
    
    def _uninstall_package(self, language: str, pm: str, package: str) -> str:
        """Uninstall a package using the specified package manager."""
        if language not in self.package_managers:
            return f"Error: Unsupported language '{language}'"
        
        if pm not in self.package_managers[language]:
            return f"Error: Unsupported package manager '{pm}' for {language}"
        
        if not self._check_pm_available(pm):
            return f"Error: Package manager '{pm}' is not installed or not in PATH"
        
        pm_config = self.package_managers[language][pm]
        if 'uninstall' not in pm_config:
            return f"Error: Uninstall not supported for {pm}"
        
        uninstall_cmd = pm_config['uninstall']
        cmd = f"{uninstall_cmd} {package}"
        
        try:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                return f"✅ Successfully uninstalled {package}\n\nOutput:\n{result.stdout}"
            else:
                return f"❌ Failed to uninstall {package}\n\nError:\n{result.stderr}"
        
        except subprocess.TimeoutExpired:
            return f"❌ Uninstallation of {package} timed out"
        except Exception as e:
            return f"❌ Error uninstalling {package}: {str(e)}"
    
    def _list_packages(self, language: str, pm: str) -> str:
        """List installed packages."""
        if language not in self.package_managers:
            return f"Error: Unsupported language '{language}'"
        
        if pm not in self.package_managers[language]:
            return f"Error: Unsupported package manager '{pm}' for {language}"
        
        if not self._check_pm_available(pm):
            return f"Error: Package manager '{pm}' is not installed or not in PATH"
        
        pm_config = self.package_managers[language][pm]
        list_cmd = pm_config['list']
        
        try:
            result = subprocess.run(list_cmd, shell=True, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                return f"📦 Installed packages ({pm}):\n\n{result.stdout}"
            else:
                return f"❌ Failed to list packages\n\nError:\n{result.stderr}"
        
        except subprocess.TimeoutExpired:
            return "❌ Package listing timed out"
        except Exception as e:
            return f"❌ Error listing packages: {str(e)}"
    
    def _update_packages(self, language: str, pm: str, package: Optional[str] = None) -> str:
        """Update packages."""
        if language not in self.package_managers:
            return f"Error: Unsupported language '{language}'"
        
        if pm not in self.package_managers[language]:
            return f"Error: Unsupported package manager '{pm}' for {language}"
        
        if not self._check_pm_available(pm):
            return f"Error: Package manager '{pm}' is not installed or not in PATH"
        
        pm_config = self.package_managers[language][pm]
        update_cmd = pm_config['update']
        
        if package:
            cmd = f"{update_cmd} {package}"
        else:
            cmd = update_cmd
        
        try:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                target = package if package else "all packages"
                return f"✅ Successfully updated {target}\n\nOutput:\n{result.stdout}"
            else:
                return f"❌ Failed to update packages\n\nError:\n{result.stderr}"
        
        except subprocess.TimeoutExpired:
            return "❌ Package update timed out"
        except Exception as e:
            return f"❌ Error updating packages: {str(e)}"
    
    def _detect_project_type(self, directory: str) -> str:
        """Auto-detect project type and package manager from directory contents."""
        if not os.path.exists(directory):
            return f"Error: Directory '{directory}' does not exist"
        
        detected = []
        
        # Check for various project files
        project_indicators = {
            'python': {
                'requirements.txt': 'pip',
                'pyproject.toml': 'poetry',
                'environment.yml': 'conda',
                'Pipfile': 'pipenv',
                'setup.py': 'pip'
            },
            'javascript': {
                'package.json': 'npm',
                'yarn.lock': 'yarn',
                'pnpm-lock.yaml': 'pnpm'
            },
            'java': {
                'pom.xml': 'maven',
                'build.gradle': 'gradle',
                'build.gradle.kts': 'gradle'
            },
            'go': {
                'go.mod': 'go'
            },
            'rust': {
                'Cargo.toml': 'cargo'
            },
            'ruby': {
                'Gemfile': 'bundle',
                '*.gemspec': 'gem'
            },
            'php': {
                'composer.json': 'composer'
            },
            'csharp': {
                '*.csproj': 'dotnet',
                '*.sln': 'dotnet'
            }
        }
        
        for language, indicators in project_indicators.items():
            for file_pattern, pm in indicators.items():
                if '*' in file_pattern:
                    # Handle glob patterns
                    import glob
                    matches = glob.glob(os.path.join(directory, file_pattern))
                    if matches:
                        detected.append((language, pm, matches[0]))
                else:
                    file_path = os.path.join(directory, file_pattern)
                    if os.path.exists(file_path):
                        detected.append((language, pm, file_pattern))
        
        if not detected:
            return f"No recognized project files found in '{directory}'"
        
        result = f"🔍 Project Detection Results for '{directory}':\n\n"
        for language, pm, file in detected:
            result += f"  {language.title()}: {pm} (found {file})\n"
        
        # Add recommendations
        result += "\n💡 Recommendations:\n"
        for language, pm, file in detected:
            result += f"  Use: package_manager list {language} {pm}\n"
        
        return result
    
    def _init_project(self, language: str, pm: str) -> str:
        """Initialize a new project with the specified package manager."""
        if language not in self.package_managers:
            return f"Error: Unsupported language '{language}'"
        
        if pm not in self.package_managers[language]:
            return f"Error: Unsupported package manager '{pm}' for {language}"
        
        if not self._check_pm_available(pm):
            return f"Error: Package manager '{pm}' is not installed or not in PATH"
        
        init_commands = {
            'npm': 'npm init -y',
            'yarn': 'yarn init -y',
            'pnpm': 'pnpm init',
            'poetry': 'poetry init --no-interaction',
            'cargo': 'cargo init',
            'go': 'go mod init project',
            'maven': 'mvn archetype:generate -DgroupId=com.example -DartifactId=my-app -DarchetypeArtifactId=maven-archetype-quickstart -DinteractiveMode=false',
            'composer': 'composer init --no-interaction',
            'dotnet': 'dotnet new console'
        }
        
        if pm not in init_commands:
            return f"Error: Project initialization not supported for {pm}"
        
        cmd = init_commands[pm]
        
        try:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                return f"✅ Successfully initialized {language} project with {pm}\n\nOutput:\n{result.stdout}"
            else:
                return f"❌ Failed to initialize project\n\nError:\n{result.stderr}"
        
        except subprocess.TimeoutExpired:
            return "❌ Project initialization timed out"
        except Exception as e:
            return f"❌ Error initializing project: {str(e)}"
    
    def _audit_dependencies(self, language: str, pm: str) -> str:
        """Perform security audit of dependencies."""
        if language not in self.package_managers:
            return f"Error: Unsupported language '{language}'"
        
        if pm not in self.package_managers[language]:
            return f"Error: Unsupported package manager '{pm}' for {language}"
        
        if not self._check_pm_available(pm):
            return f"Error: Package manager '{pm}' is not installed or not in PATH"
        
        audit_commands = {
            'npm': 'npm audit',
            'yarn': 'yarn audit',
            'pip': 'pip-audit',  # Requires pip-audit package
            'cargo': 'cargo audit',  # Requires cargo-audit
            'bundle': 'bundle audit'
        }
        
        if pm not in audit_commands:
            return f"Security audit not available for {pm}"
        
        cmd = audit_commands[pm]
        
        try:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=120)
            
            # Some audit commands return non-zero exit codes when vulnerabilities are found
            if result.returncode == 0 or (pm in ['npm', 'yarn'] and result.stdout):
                return f"🔒 Security Audit Results ({pm}):\n\n{result.stdout}"
            else:
                return f"❌ Security audit failed or found issues\n\nOutput:\n{result.stderr}\n{result.stdout}"
        
        except subprocess.TimeoutExpired:
            return "❌ Security audit timed out"
        except Exception as e:
            return f"❌ Error running security audit: {str(e)}"
    
    def _check_pm_available(self, pm: str) -> bool:
        """Check if a package manager is available in the system."""
        try:
            result = subprocess.run(f"{pm} --version", shell=True, capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except:
            return False


if __name__ == "__main__":
    # Test the package manager
    pm = PackageManager()
    result = pm.execute(["detect ."])
    print(result)
