import os
import sys
import ast
import re
import inspect
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path

if __name__ == "__main__":
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from sources.tools.tools import Tools

class TestGenerator(Tools):
    """
    Automated test generation tool that creates unit tests, integration tests,
    and test suites for various programming languages.
    """

    def __init__(self):
        super().__init__()
        self.tag = "test_generator"
        self.name = "Test Generator"
        self.description = "Automated test generation for unit tests, integration tests, and test suites"

        # Test framework templates
        self.test_frameworks = {
            'python': {
                'unittest': {
                    'import': 'import unittest',
                    'class_template': 'class Test{class_name}(unittest.TestCase):',
                    'method_template': '    def test_{method_name}(self):',
                    'assert_template': '        self.assertEqual({actual}, {expected})',
                    'setup': '    def setUp(self):\n        pass',
                    'teardown': '    def tearDown(self):\n        pass'
                },
                'pytest': {
                    'import': 'import pytest',
                    'class_template': 'class Test{class_name}:',
                    'method_template': '    def test_{method_name}(self):',
                    'assert_template': '        assert {actual} == {expected}',
                    'fixture': '    @pytest.fixture\n    def setup(self):\n        pass'
                }
            },
            'javascript': {
                'jest': {
                    'describe_template': "describe('{class_name}', () => {{",
                    'test_template': "  test('{test_name}', () => {{",
                    'expect_template': "    expect({actual}).toBe({expected});",
                    'setup': "  beforeEach(() => {\n    // Setup\n  });",
                    'teardown': "  afterEach(() => {\n    // Cleanup\n  });"
                },
                'mocha': {
                    'describe_template': "describe('{class_name}', function() {{",
                    'test_template': "  it('{test_name}', function() {{",
                    'assert_template': "    assert.equal({actual}, {expected});",
                    'import': "const assert = require('assert');"
                }
            },
            'java': {
                'junit': {
                    'import': 'import org.junit.jupiter.api.Test;\nimport static org.junit.jupiter.api.Assertions.*;',
                    'class_template': 'public class {class_name}Test {{',
                    'method_template': '    @Test\n    public void test{method_name}() {{',
                    'assert_template': '        assertEquals({expected}, {actual});',
                    'setup': '    @BeforeEach\n    public void setUp() {\n        // Setup\n    }'
                }
            }
        }

    def execute(self, blocks: List[str], safety: bool = False) -> str:
        """
        Execute test generation commands.

        Commands:
        - generate <file_path> [framework] - Generate tests for a file
        - unit <file_path> [framework] - Generate unit tests
        - integration <file_path> [framework] - Generate integration tests
        - mock <file_path> [framework] - Generate tests with mocks
        - coverage <file_path> - Generate coverage report
        - template <language> <framework> - Show test template
        """
        if not blocks:
            return self._get_help()

        command_line = blocks[0].strip()
        parts = command_line.split()

        if len(parts) < 1:
            return self._get_help()

        command = parts[0].lower()

        try:
            if command == "generate":
                if len(parts) < 2:
                    return "Error: Please specify a file path to generate tests for"
                file_path = parts[1]
                framework = parts[2] if len(parts) > 2 else None
                return self._generate_tests(file_path, framework)

            elif command == "unit":
                if len(parts) < 2:
                    return "Error: Please specify a file path for unit tests"
                file_path = parts[1]
                framework = parts[2] if len(parts) > 2 else None
                return self._generate_unit_tests(file_path, framework)

            elif command == "integration":
                if len(parts) < 2:
                    return "Error: Please specify a file path for integration tests"
                file_path = parts[1]
                framework = parts[2] if len(parts) > 2 else None
                return self._generate_integration_tests(file_path, framework)

            elif command == "mock":
                if len(parts) < 2:
                    return "Error: Please specify a file path for mock tests"
                file_path = parts[1]
                framework = parts[2] if len(parts) > 2 else None
                return self._generate_mock_tests(file_path, framework)

            elif command == "coverage":
                if len(parts) < 2:
                    return "Error: Please specify a file path for coverage analysis"
                return self._generate_coverage_report(parts[1])

            elif command == "template":
                if len(parts) < 3:
                    return "Error: Please specify language and framework"
                return self._show_template(parts[1], parts[2])

            else:
                return f"Unknown command: {command}\n\n{self._get_help()}"

        except Exception as e:
            return f"Error during test generation: {str(e)}"

    def _get_help(self) -> str:
        """Return help information for the test generator."""
        return """Test Generator Commands:

generate <file_path> [framework]     - Generate comprehensive tests
unit <file_path> [framework]         - Generate unit tests only
integration <file_path> [framework]  - Generate integration tests
mock <file_path> [framework]         - Generate tests with mocks
coverage <file_path>                 - Generate coverage report
template <language> <framework>      - Show test template

Supported Languages & Frameworks:
- Python: unittest, pytest
- JavaScript: jest, mocha
- Java: junit

Examples:
```test_generator
generate src/calculator.py pytest
```

```test_generator
unit MyClass.java junit
```

```test_generator
mock api_client.js jest
```
"""

    def _detect_language(self, file_path: str) -> Optional[str]:
        """Detect programming language from file extension."""
        ext = Path(file_path).suffix.lower()
        language_map = {
            '.py': 'python',
            '.js': 'javascript',
            '.jsx': 'javascript',
            '.ts': 'javascript',
            '.tsx': 'javascript',
            '.java': 'java',
            '.c': 'c',
            '.cpp': 'cpp',
            '.go': 'go',
            '.rs': 'rust'
        }
        return language_map.get(ext)

    def _generate_tests(self, file_path: str, framework: Optional[str] = None) -> str:
        """Generate comprehensive tests for a file."""
        if not os.path.exists(file_path):
            return f"Error: File '{file_path}' not found"

        language = self._detect_language(file_path)
        if not language:
            return f"Error: Unsupported file type for '{file_path}'"

        if language not in self.test_frameworks:
            return f"Error: Test generation not supported for {language}"

        # Auto-detect framework if not specified
        if not framework:
            framework = self._detect_framework(language)

        if framework not in self.test_frameworks[language]:
            available = list(self.test_frameworks[language].keys())
            return f"Error: Framework '{framework}' not supported for {language}. Available: {available}"

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            return f"Error reading file: {str(e)}"

        # Parse the file and extract testable elements
        if language == 'python':
            return self._generate_python_tests(content, file_path, framework)
        elif language == 'javascript':
            return self._generate_javascript_tests(content, file_path, framework)
        elif language == 'java':
            return self._generate_java_tests(content, file_path, framework)
        else:
            return f"Test generation not implemented for {language}"

    def _generate_unit_tests(self, file_path: str, framework: Optional[str] = None) -> str:
        """Generate unit tests specifically."""
        return self._generate_tests(file_path, framework)

    def _generate_integration_tests(self, file_path: str, framework: Optional[str] = None) -> str:
        """Generate integration tests."""
        base_tests = self._generate_tests(file_path, framework)

        integration_note = """
# Integration Test Notes:
# - Test interactions between components
# - Use real dependencies where appropriate
# - Test data flow and side effects
# - Consider testing with external services (databases, APIs)
"""

        return f"{integration_note}\n{base_tests}"

    def _generate_mock_tests(self, file_path: str, framework: Optional[str] = None) -> str:
        """Generate tests with mocking."""
        language = self._detect_language(file_path)
        base_tests = self._generate_tests(file_path, framework)

        mock_imports = {
            'python': "from unittest.mock import Mock, patch, MagicMock",
            'javascript': "const { jest } = require('@jest/globals');",
            'java': "import org.mockito.Mock;\nimport org.mockito.MockitoAnnotations;"
        }

        mock_note = f"""
# Mock Test Setup
{mock_imports.get(language, '# Add appropriate mock imports')}

# Mock examples:
# - Mock external dependencies
# - Mock database calls
# - Mock API responses
# - Mock file system operations
"""

        return f"{mock_note}\n{base_tests}"

    def _generate_coverage_report(self, file_path: str) -> str:
        """Generate coverage analysis and report."""
        language = self._detect_language(file_path)

        coverage_commands = {
            'python': {
                'install': 'pip install coverage',
                'run': f'coverage run -m pytest test_{Path(file_path).stem}.py',
                'report': 'coverage report -m',
                'html': 'coverage html'
            },
            'javascript': {
                'jest': 'npx jest --coverage',
                'nyc': 'npx nyc mocha test/*.js'
            },
            'java': {
                'jacoco': 'mvn jacoco:report',
                'gradle': 'gradle jacocoTestReport'
            }
        }

        if language not in coverage_commands:
            return f"Coverage analysis not available for {language}"

        commands = coverage_commands[language]

        result = f"=== COVERAGE ANALYSIS FOR {file_path} ===\n\n"

        if language == 'python':
            result += f"""To generate coverage report:

1. Install coverage: {commands['install']}
2. Run tests with coverage: {commands['run']}
3. Generate report: {commands['report']}
4. Generate HTML report: {commands['html']}

Coverage will show:
- Line coverage percentage
- Missing lines
- Branch coverage
- Function coverage
"""

        elif language == 'javascript':
            result += f"""To generate coverage report:

Jest: {commands['jest']}
NYC: {commands['nyc']}

Coverage will include:
- Statement coverage
- Branch coverage
- Function coverage
- Line coverage
"""

        elif language == 'java':
            result += f"""To generate coverage report:

Maven: {commands['jacoco']}
Gradle: {commands['gradle']}

JaCoCo will provide:
- Instruction coverage
- Branch coverage
- Line coverage
- Method coverage
- Class coverage
"""

        return result

    def _show_template(self, language: str, framework: str) -> str:
        """Show test template for a language and framework."""
        if language not in self.test_frameworks:
            available_langs = list(self.test_frameworks.keys())
            return f"Language '{language}' not supported. Available: {available_langs}"

        if framework not in self.test_frameworks[language]:
            available_frameworks = list(self.test_frameworks[language].keys())
            return f"Framework '{framework}' not supported for {language}. Available: {available_frameworks}"

        template = self.test_frameworks[language][framework]

        result = f"=== {language.upper()} {framework.upper()} TEMPLATE ===\n\n"

        for key, value in template.items():
            result += f"{key.upper()}:\n{value}\n\n"

        return result

    def _detect_framework(self, language: str) -> str:
        """Auto-detect the most appropriate test framework."""
        defaults = {
            'python': 'pytest',
            'javascript': 'jest',
            'java': 'junit'
        }
        return defaults.get(language, list(self.test_frameworks[language].keys())[0])

    def _generate_python_tests(self, content: str, file_path: str, framework: str) -> str:
        """Generate Python tests."""
        try:
            tree = ast.parse(content)
            analyzer = PythonTestAnalyzer()
            analyzer.visit(tree)

            classes = analyzer.classes
            functions = analyzer.functions

        except SyntaxError as e:
            return f"Syntax error in Python file: {str(e)}"

        template = self.test_frameworks['python'][framework]
        file_name = Path(file_path).stem

        test_code = f"# Generated tests for {file_path}\n"
        test_code += f"{template['import']}\n"
        test_code += f"from {file_name} import *\n\n"

        # Generate tests for classes
        for class_info in classes:
            class_name = class_info['name']
            test_code += template['class_template'].format(class_name=class_name) + "\n"

            if framework == 'unittest':
                test_code += template['setup'] + "\n\n"

            # Generate tests for class methods
            for method in class_info['methods']:
                method_name = method['name']
                if not method_name.startswith('_'):  # Skip private methods
                    test_code += template['method_template'].format(method_name=method_name) + "\n"
                    test_code += f"        # Test {method_name} method\n"
                    test_code += "        # TODO: Add test implementation\n"
                    test_code += "        pass\n\n"

            if framework == 'unittest':
                test_code += template['teardown'] + "\n"

            test_code += "\n"

        # Generate tests for standalone functions
        if functions:
            if framework == 'unittest':
                test_code += "class TestFunctions(unittest.TestCase):\n"
            else:
                test_code += "class TestFunctions:\n"

            for func in functions:
                func_name = func['name']
                if not func_name.startswith('_'):  # Skip private functions
                    test_code += template['method_template'].format(method_name=func_name) + "\n"
                    test_code += f"        # Test {func_name} function\n"
                    test_code += "        # TODO: Add test implementation\n"
                    test_code += "        pass\n\n"

        if framework == 'unittest':
            test_code += "\nif __name__ == '__main__':\n    unittest.main()\n"

        return test_code

    def _generate_javascript_tests(self, content: str, file_path: str, framework: str) -> str:
        """Generate JavaScript tests."""
        template = self.test_frameworks['javascript'][framework]
        file_name = Path(file_path).stem

        # Simple regex-based parsing for JavaScript
        classes = re.findall(r'class\s+(\w+)', content)
        functions = re.findall(r'function\s+(\w+)', content)
        arrow_functions = re.findall(r'const\s+(\w+)\s*=\s*\([^)]*\)\s*=>', content)

        test_code = f"// Generated tests for {file_path}\n"
        if framework == 'mocha':
            test_code += template.get('import', '') + "\n"
        test_code += f"const {file_name} = require('./{file_name}');\n\n"

        # Generate tests for classes
        for class_name in classes:
            test_code += template['describe_template'].format(class_name=class_name) + "\n"
            test_code += template.get('setup', '') + "\n\n"

            test_code += template['test_template'].format(test_name=f'should create {class_name} instance') + "\n"
            test_code += f"    // Test {class_name} instantiation\n"
            test_code += "    // TODO: Add test implementation\n"
            test_code += "  });\n\n"

            test_code += "});\n\n"

        # Generate tests for functions
        all_functions = functions + arrow_functions
        if all_functions:
            test_code += template['describe_template'].format(class_name='Functions') + "\n"

            for func_name in all_functions:
                test_code += template['test_template'].format(test_name=f'should test {func_name}') + "\n"
                test_code += f"    // Test {func_name} function\n"
                test_code += "    // TODO: Add test implementation\n"
                test_code += "  });\n\n"

            test_code += "});\n"

        return test_code

    def _generate_java_tests(self, content: str, file_path: str, framework: str) -> str:
        """Generate Java tests."""
        template = self.test_frameworks['java'][framework]

        # Simple regex-based parsing for Java
        class_match = re.search(r'public\s+class\s+(\w+)', content)
        methods = re.findall(r'public\s+\w+\s+(\w+)\s*\([^)]*\)', content)

        if not class_match:
            return "Error: No public class found in Java file"

        class_name = class_match.group(1)

        test_code = f"// Generated tests for {file_path}\n"
        test_code += template['import'] + "\n\n"
        test_code += template['class_template'].format(class_name=class_name) + "\n"
        test_code += f"    private {class_name} {class_name.lower()};\n\n"
        test_code += template.get('setup', '').replace('setUp', 'setUp').replace('// Setup', f'{class_name.lower()} = new {class_name}();') + "\n\n"

        # Generate tests for methods
        for method_name in methods:
            if method_name not in ['main', 'toString', 'equals', 'hashCode']:
                test_code += template['method_template'].format(method_name=method_name.title()) + "\n"
                test_code += f"        // Test {method_name} method\n"
                test_code += "        // TODO: Add test implementation\n"
                test_code += "    }\n\n"

        test_code += "}\n"

        return test_code


class PythonTestAnalyzer(ast.NodeVisitor):
    """AST visitor for analyzing Python code structure for test generation."""

    def __init__(self):
        self.classes = []
        self.functions = []
        self.current_class = None

    def visit_ClassDef(self, node):
        class_info = {
            'name': node.name,
            'methods': [],
            'line': node.lineno
        }

        old_class = self.current_class
        self.current_class = class_info

        self.generic_visit(node)

        self.classes.append(class_info)
        self.current_class = old_class

    def visit_FunctionDef(self, node):
        func_info = {
            'name': node.name,
            'args': [arg.arg for arg in node.args.args],
            'line': node.lineno
        }

        if self.current_class:
            self.current_class['methods'].append(func_info)
        else:
            self.functions.append(func_info)

        self.generic_visit(node)


if __name__ == "__main__":
    # Test the test generator
    generator = TestGenerator()
    result = generator.execute(["generate test.py pytest"])
    print(result)