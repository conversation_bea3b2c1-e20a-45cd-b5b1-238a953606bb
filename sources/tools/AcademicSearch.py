import os
import sys
import requests
import json
import re
from typing import Dict, List, Optional, Tuple, Any
from urllib.parse import quote, urlencode
import time

if __name__ == "__main__":
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from sources.tools.tools import Tools

class AcademicSearch(Tools):
    """
    Advanced academic research tool for searching and analyzing research papers,
    academic databases, and scholarly content.
    """
    
    def __init__(self):
        super().__init__()
        self.tag = "academic_search"
        self.name = "Academic Search"
        self.description = "Advanced academic research and paper analysis tool"
        
        # Academic search endpoints
        self.search_apis = {
            'arxiv': 'http://export.arxiv.org/api/query',
            'crossref': 'https://api.crossref.org/works',
            'semantic_scholar': 'https://api.semanticscholar.org/graph/v1/paper/search',
            'pubmed': 'https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi'
        }
        
        # Rate limiting
        self.last_request_time = {}
        self.min_request_interval = 1.0  # seconds between requests
    
    def execute(self, blocks: List[str], safety: bool = False) -> str:
        """
        Execute academic search commands.
        
        Commands:
        - search <query> [source] [limit] - Search academic papers
        - arxiv <query> [limit] - Search arXiv papers
        - pubmed <query> [limit] - Search PubMed articles
        - crossref <query> [limit] - Search CrossRef database
        - semantic <query> [limit] - Search Semantic Scholar
        - paper <paper_id> [source] - Get specific paper details
        - citations <paper_id> - Get paper citations
        - trends <topic> - Analyze research trends
        - authors <author_name> - Search by author
        - venue <venue_name> - Search by publication venue
        """
        if not blocks:
            return self._get_help()
        
        command_line = blocks[0].strip()
        parts = command_line.split()
        
        if len(parts) < 1:
            return self._get_help()
        
        command = parts[0].lower()
        
        try:
            if command == "search":
                if len(parts) < 2:
                    return "Error: Please specify a search query"
                query = ' '.join(parts[1:])
                source = parts[2] if len(parts) > 2 and parts[2] in self.search_apis else "arxiv"
                limit = int(parts[3]) if len(parts) > 3 and parts[3].isdigit() else 10
                return self._search_papers(query, source, limit)
            
            elif command == "arxiv":
                if len(parts) < 2:
                    return "Error: Please specify a search query for arXiv"
                query = ' '.join(parts[1:])
                limit = int(parts[-1]) if parts[-1].isdigit() else 10
                if parts[-1].isdigit():
                    query = ' '.join(parts[1:-1])
                return self._search_arxiv(query, limit)
            
            elif command == "pubmed":
                if len(parts) < 2:
                    return "Error: Please specify a search query for PubMed"
                query = ' '.join(parts[1:])
                limit = int(parts[-1]) if parts[-1].isdigit() else 10
                if parts[-1].isdigit():
                    query = ' '.join(parts[1:-1])
                return self._search_pubmed(query, limit)
            
            elif command == "crossref":
                if len(parts) < 2:
                    return "Error: Please specify a search query for CrossRef"
                query = ' '.join(parts[1:])
                limit = int(parts[-1]) if parts[-1].isdigit() else 10
                if parts[-1].isdigit():
                    query = ' '.join(parts[1:-1])
                return self._search_crossref(query, limit)
            
            elif command == "semantic":
                if len(parts) < 2:
                    return "Error: Please specify a search query for Semantic Scholar"
                query = ' '.join(parts[1:])
                limit = int(parts[-1]) if parts[-1].isdigit() else 10
                if parts[-1].isdigit():
                    query = ' '.join(parts[1:-1])
                return self._search_semantic_scholar(query, limit)
            
            elif command == "paper":
                if len(parts) < 2:
                    return "Error: Please specify a paper ID"
                paper_id = parts[1]
                source = parts[2] if len(parts) > 2 else "arxiv"
                return self._get_paper_details(paper_id, source)
            
            elif command == "citations":
                if len(parts) < 2:
                    return "Error: Please specify a paper ID for citation analysis"
                return self._get_citations(parts[1])
            
            elif command == "trends":
                if len(parts) < 2:
                    return "Error: Please specify a topic for trend analysis"
                topic = ' '.join(parts[1:])
                return self._analyze_trends(topic)
            
            elif command == "authors":
                if len(parts) < 2:
                    return "Error: Please specify an author name"
                author = ' '.join(parts[1:])
                return self._search_by_author(author)
            
            elif command == "venue":
                if len(parts) < 2:
                    return "Error: Please specify a venue name"
                venue = ' '.join(parts[1:])
                return self._search_by_venue(venue)
            
            else:
                return f"Unknown command: {command}\n\n{self._get_help()}"
        
        except Exception as e:
            return f"Error during academic search: {str(e)}"
    
    def _get_help(self) -> str:
        """Return help information for the academic search tool."""
        return """Academic Search Commands:

search <query> [source] [limit]    - Search academic papers (default: arxiv, 10)
arxiv <query> [limit]              - Search arXiv papers
pubmed <query> [limit]             - Search PubMed articles  
crossref <query> [limit]           - Search CrossRef database
semantic <query> [limit]           - Search Semantic Scholar
paper <paper_id> [source]          - Get specific paper details
citations <paper_id>               - Get paper citations
trends <topic>                     - Analyze research trends
authors <author_name>              - Search by author
venue <venue_name>                 - Search by publication venue

Available Sources: arxiv, pubmed, crossref, semantic_scholar

Examples:
```academic_search
search "machine learning" arxiv 5
```

```academic_search
arxiv "neural networks" 10
```

```academic_search
authors "Geoffrey Hinton"
```

```academic_search
trends "artificial intelligence"
```
"""
    
    def _rate_limit(self, source: str):
        """Implement rate limiting for API requests."""
        current_time = time.time()
        if source in self.last_request_time:
            time_since_last = current_time - self.last_request_time[source]
            if time_since_last < self.min_request_interval:
                time.sleep(self.min_request_interval - time_since_last)
        self.last_request_time[source] = time.time()
    
    def _search_papers(self, query: str, source: str, limit: int) -> str:
        """Search papers from specified source."""
        if source == "arxiv":
            return self._search_arxiv(query, limit)
        elif source == "pubmed":
            return self._search_pubmed(query, limit)
        elif source == "crossref":
            return self._search_crossref(query, limit)
        elif source == "semantic_scholar":
            return self._search_semantic_scholar(query, limit)
        else:
            return f"Error: Unknown source '{source}'"
    
    def _search_arxiv(self, query: str, limit: int) -> str:
        """Search arXiv papers."""
        self._rate_limit('arxiv')
        
        try:
            # Construct arXiv API query
            params = {
                'search_query': f'all:{query}',
                'start': 0,
                'max_results': limit,
                'sortBy': 'relevance',
                'sortOrder': 'descending'
            }
            
            url = f"{self.search_apis['arxiv']}?{urlencode(params)}"
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            # Parse XML response
            papers = self._parse_arxiv_response(response.text)
            
            return self._format_arxiv_results(query, papers, limit)
        
        except Exception as e:
            return f"Error searching arXiv: {str(e)}"
    
    def _search_pubmed(self, query: str, limit: int) -> str:
        """Search PubMed articles."""
        self._rate_limit('pubmed')
        
        try:
            # PubMed E-utilities search
            params = {
                'db': 'pubmed',
                'term': query,
                'retmax': limit,
                'retmode': 'json',
                'sort': 'relevance'
            }
            
            url = f"{self.search_apis['pubmed']}?{urlencode(params)}"
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            if 'esearchresult' in data and 'idlist' in data['esearchresult']:
                ids = data['esearchresult']['idlist']
                return self._get_pubmed_details(ids, query)
            else:
                return f"No PubMed articles found for query: '{query}'"
        
        except Exception as e:
            return f"Error searching PubMed: {str(e)}"
    
    def _search_crossref(self, query: str, limit: int) -> str:
        """Search CrossRef database."""
        self._rate_limit('crossref')
        
        try:
            params = {
                'query': query,
                'rows': limit,
                'sort': 'relevance',
                'order': 'desc'
            }
            
            url = f"{self.search_apis['crossref']}?{urlencode(params)}"
            headers = {'User-Agent': 'AcademicSearch/1.0 (mailto:<EMAIL>)'}
            
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            if 'message' in data and 'items' in data['message']:
                papers = data['message']['items']
                return self._format_crossref_results(query, papers)
            else:
                return f"No CrossRef articles found for query: '{query}'"
        
        except Exception as e:
            return f"Error searching CrossRef: {str(e)}"
    
    def _search_semantic_scholar(self, query: str, limit: int) -> str:
        """Search Semantic Scholar."""
        self._rate_limit('semantic_scholar')
        
        try:
            params = {
                'query': query,
                'limit': limit,
                'fields': 'paperId,title,authors,year,abstract,citationCount,url'
            }
            
            url = f"{self.search_apis['semantic_scholar']}?{urlencode(params)}"
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            if 'data' in data:
                papers = data['data']
                return self._format_semantic_scholar_results(query, papers)
            else:
                return f"No Semantic Scholar papers found for query: '{query}'"
        
        except Exception as e:
            return f"Error searching Semantic Scholar: {str(e)}"
    
    def _get_paper_details(self, paper_id: str, source: str) -> str:
        """Get detailed information about a specific paper."""
        if source == "arxiv":
            return self._get_arxiv_paper(paper_id)
        elif source == "semantic_scholar":
            return self._get_semantic_scholar_paper(paper_id)
        else:
            return f"Paper details not implemented for source: {source}"
    
    def _get_citations(self, paper_id: str) -> str:
        """Get citation information for a paper."""
        try:
            # Use Semantic Scholar API for citation data
            url = f"https://api.semanticscholar.org/graph/v1/paper/{paper_id}/citations"
            params = {'fields': 'title,authors,year,citationCount'}
            
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            if 'data' in data:
                citations = data['data']
                return self._format_citation_results(paper_id, citations)
            else:
                return f"No citation data found for paper: {paper_id}"
        
        except Exception as e:
            return f"Error getting citations: {str(e)}"
    
    def _analyze_trends(self, topic: str) -> str:
        """Analyze research trends for a topic."""
        # Search recent papers and analyze publication patterns
        recent_papers = self._search_arxiv(f"{topic} AND submittedDate:[2020 TO 2024]", 50)
        
        trend_analysis = f"""=== RESEARCH TREND ANALYSIS ===

Topic: {topic}
Analysis Period: 2020-2024

{recent_papers}

=== TREND INSIGHTS ===

To get comprehensive trend analysis:
1. Publication volume over time
2. Key authors and institutions
3. Emerging subtopics and keywords
4. Citation patterns and impact
5. Geographic distribution of research

Note: This is a basic trend analysis. For detailed trends, consider using specialized bibliometric tools.
"""
        
        return trend_analysis
    
    def _search_by_author(self, author: str) -> str:
        """Search papers by author name."""
        # Search across multiple sources
        arxiv_results = self._search_arxiv(f"au:{author}", 10)
        semantic_results = self._search_semantic_scholar(f"author:{author}", 10)
        
        return f"""=== AUTHOR SEARCH RESULTS ===

Author: {author}

=== ARXIV RESULTS ===
{arxiv_results}

=== SEMANTIC SCHOLAR RESULTS ===
{semantic_results}
"""
    
    def _search_by_venue(self, venue: str) -> str:
        """Search papers by publication venue."""
        crossref_results = self._search_crossref(f"container-title:{venue}", 10)
        
        return f"""=== VENUE SEARCH RESULTS ===

Venue: {venue}

{crossref_results}
"""
    
    # Parsing and formatting methods
    def _parse_arxiv_response(self, xml_content: str) -> List[Dict]:
        """Parse arXiv XML response."""
        papers = []
        
        # Simple XML parsing (in production, use proper XML parser)
        entries = re.findall(r'<entry>(.*?)</entry>', xml_content, re.DOTALL)
        
        for entry in entries:
            paper = {}
            
            # Extract title
            title_match = re.search(r'<title>(.*?)</title>', entry, re.DOTALL)
            if title_match:
                paper['title'] = title_match.group(1).strip().replace('\n', ' ')
            
            # Extract authors
            authors = re.findall(r'<name>(.*?)</name>', entry)
            paper['authors'] = authors
            
            # Extract abstract
            summary_match = re.search(r'<summary>(.*?)</summary>', entry, re.DOTALL)
            if summary_match:
                paper['abstract'] = summary_match.group(1).strip().replace('\n', ' ')
            
            # Extract arXiv ID
            id_match = re.search(r'<id>(.*?)</id>', entry)
            if id_match:
                paper['id'] = id_match.group(1).split('/')[-1]
            
            # Extract published date
            published_match = re.search(r'<published>(.*?)</published>', entry)
            if published_match:
                paper['published'] = published_match.group(1)[:10]  # YYYY-MM-DD
            
            papers.append(paper)
        
        return papers
    
    def _get_pubmed_details(self, ids: List[str], query: str) -> str:
        """Get detailed information for PubMed articles."""
        if not ids:
            return f"No PubMed articles found for query: '{query}'"
        
        try:
            # Fetch details using efetch
            ids_str = ','.join(ids[:10])  # Limit to first 10
            fetch_url = f"https://eutils.ncbi.nlm.nih.gov/entrez/eutils/efetch.fcgi"
            params = {
                'db': 'pubmed',
                'id': ids_str,
                'retmode': 'xml'
            }
            
            response = requests.get(fetch_url, params=params, timeout=30)
            response.raise_for_status()
            
            # Parse XML and extract basic information
            articles = re.findall(r'<PubmedArticle>(.*?)</PubmedArticle>', response.text, re.DOTALL)
            
            result = f"=== PUBMED SEARCH RESULTS ===\n\nQuery: '{query}'\nResults: {len(articles)}\n\n"
            
            for i, article in enumerate(articles[:5], 1):  # Show first 5
                title_match = re.search(r'<ArticleTitle>(.*?)</ArticleTitle>', article, re.DOTALL)
                title = title_match.group(1) if title_match else "No title"
                
                result += f"{i}. {title}\n"
                result += f"   PMID: {ids[i-1]}\n\n"
            
            return result
        
        except Exception as e:
            return f"Error fetching PubMed details: {str(e)}"
    
    def _format_arxiv_results(self, query: str, papers: List[Dict], limit: int) -> str:
        """Format arXiv search results."""
        if not papers:
            return f"No arXiv papers found for query: '{query}'"
        
        result = f"=== ARXIV SEARCH RESULTS ===\n\nQuery: '{query}'\nResults: {len(papers)}/{limit}\n\n"
        
        for i, paper in enumerate(papers, 1):
            result += f"{i}. **{paper.get('title', 'No title')}**\n"
            
            if paper.get('authors'):
                authors_str = ', '.join(paper['authors'][:3])
                if len(paper['authors']) > 3:
                    authors_str += f" et al. ({len(paper['authors'])} authors)"
                result += f"   Authors: <AUTHORS>
            
            if paper.get('published'):
                result += f"   Published: {paper['published']}\n"
            
            if paper.get('id'):
                result += f"   arXiv ID: {paper['id']}\n"
            
            if paper.get('abstract'):
                abstract = paper['abstract'][:200] + "..." if len(paper['abstract']) > 200 else paper['abstract']
                result += f"   Abstract: {abstract}\n"
            
            result += "\n"
        
        return result
    
    def _format_crossref_results(self, query: str, papers: List[Dict]) -> str:
        """Format CrossRef search results."""
        if not papers:
            return f"No CrossRef articles found for query: '{query}'"
        
        result = f"=== CROSSREF SEARCH RESULTS ===\n\nQuery: '{query}'\nResults: {len(papers)}\n\n"
        
        for i, paper in enumerate(papers, 1):
            title = paper.get('title', ['No title'])[0] if paper.get('title') else 'No title'
            result += f"{i}. **{title}**\n"
            
            if paper.get('author'):
                authors = [f"{author.get('given', '')} {author.get('family', '')}" for author in paper['author'][:3]]
                authors_str = ', '.join(authors)
                if len(paper['author']) > 3:
                    authors_str += f" et al."
                result += f"   Authors: <AUTHORS>
            
            if paper.get('published-print') or paper.get('published-online'):
                pub_date = paper.get('published-print') or paper.get('published-online')
                if pub_date and 'date-parts' in pub_date:
                    year = pub_date['date-parts'][0][0]
                    result += f"   Year: {year}\n"
            
            if paper.get('container-title'):
                journal = paper['container-title'][0]
                result += f"   Journal: {journal}\n"
            
            if paper.get('DOI'):
                result += f"   DOI: {paper['DOI']}\n"
            
            result += "\n"
        
        return result
    
    def _format_semantic_scholar_results(self, query: str, papers: List[Dict]) -> str:
        """Format Semantic Scholar search results."""
        if not papers:
            return f"No Semantic Scholar papers found for query: '{query}'"
        
        result = f"=== SEMANTIC SCHOLAR SEARCH RESULTS ===\n\nQuery: '{query}'\nResults: {len(papers)}\n\n"
        
        for i, paper in enumerate(papers, 1):
            result += f"{i}. **{paper.get('title', 'No title')}**\n"
            
            if paper.get('authors'):
                authors = [author.get('name', '') for author in paper['authors'][:3]]
                authors_str = ', '.join(authors)
                if len(paper['authors']) > 3:
                    authors_str += f" et al."
                result += f"   Authors: <AUTHORS>
            
            if paper.get('year'):
                result += f"   Year: {paper['year']}\n"
            
            if paper.get('citationCount'):
                result += f"   Citations: {paper['citationCount']}\n"
            
            if paper.get('abstract'):
                abstract = paper['abstract'][:200] + "..." if len(paper['abstract']) > 200 else paper['abstract']
                result += f"   Abstract: {abstract}\n"
            
            if paper.get('url'):
                result += f"   URL: {paper['url']}\n"
            
            result += "\n"
        
        return result
    
    def _format_citation_results(self, paper_id: str, citations: List[Dict]) -> str:
        """Format citation results."""
        if not citations:
            return f"No citations found for paper: {paper_id}"
        
        result = f"=== CITATION ANALYSIS ===\n\nPaper ID: {paper_id}\nCitations: {len(citations)}\n\n"
        
        for i, citation in enumerate(citations[:10], 1):  # Show first 10
            citing_paper = citation.get('citingPaper', {})
            result += f"{i}. **{citing_paper.get('title', 'No title')}**\n"
            
            if citing_paper.get('authors'):
                authors = [author.get('name', '') for author in citing_paper['authors'][:2]]
                result += f"   Authors: <AUTHORS>
            
            if citing_paper.get('year'):
                result += f"   Year: {citing_paper['year']}\n"
            
            result += "\n"
        
        return result
    
    def _get_arxiv_paper(self, paper_id: str) -> str:
        """Get detailed arXiv paper information."""
        return self._search_arxiv(f"id:{paper_id}", 1)
    
    def _get_semantic_scholar_paper(self, paper_id: str) -> str:
        """Get detailed Semantic Scholar paper information."""
        try:
            url = f"https://api.semanticscholar.org/graph/v1/paper/{paper_id}"
            params = {'fields': 'title,authors,year,abstract,citationCount,referenceCount,url,venue'}
            
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            paper = response.json()
            
            result = f"=== PAPER DETAILS ===\n\n"
            result += f"Title: {paper.get('title', 'No title')}\n"
            
            if paper.get('authors'):
                authors = [author.get('name', '') for author in paper['authors']]
                result += f"Authors: <AUTHORS>
            
            if paper.get('year'):
                result += f"Year: {paper['year']}\n"
            
            if paper.get('venue'):
                result += f"Venue: {paper['venue']}\n"
            
            if paper.get('citationCount'):
                result += f"Citations: {paper['citationCount']}\n"
            
            if paper.get('referenceCount'):
                result += f"References: {paper['referenceCount']}\n"
            
            if paper.get('abstract'):
                result += f"\nAbstract:\n{paper['abstract']}\n"
            
            if paper.get('url'):
                result += f"\nURL: {paper['url']}\n"
            
            return result
        
        except Exception as e:
            return f"Error getting paper details: {str(e)}"


if __name__ == "__main__":
    # Test the academic search tool
    search = AcademicSearch()
    result = search.execute(["search machine learning arxiv 5"])
    print(result)
