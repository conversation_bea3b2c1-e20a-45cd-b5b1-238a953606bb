import os
import sys
import requests
import json
import csv
import re
from typing import Dict, List, Optional, Tuple, Any
from urllib.parse import urljoin, urlparse, quote
import time
from bs4 import BeautifulSoup

if __name__ == "__main__":
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from sources.tools.tools import Tools

class DataMiner(Tools):
    """
    Advanced data mining tool for web scraping, data extraction, pattern analysis,
    and automated data collection from various sources.
    """
    
    def __init__(self):
        super().__init__()
        self.tag = "data_miner"
        self.name = "Data Miner"
        self.description = "Advanced web scraping, data extraction, and pattern analysis tool"
        
        # Session for maintaining cookies and headers
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1.0  # seconds between requests
        
        # Check dependencies
        self.dependencies = self._check_dependencies()
    
    def _check_dependencies(self) -> Dict[str, bool]:
        """Check which data mining libraries are available."""
        deps = {}
        
        try:
            import beautifulsoup4
            deps['beautifulsoup4'] = True
        except ImportError:
            deps['beautifulsoup4'] = False
        
        try:
            import pandas
            deps['pandas'] = True
        except ImportError:
            deps['pandas'] = False
        
        try:
            import selenium
            deps['selenium'] = True
        except ImportError:
            deps['selenium'] = False
        
        try:
            import scrapy
            deps['scrapy'] = True
        except ImportError:
            deps['scrapy'] = False
        
        return deps
    
    def execute(self, blocks: List[str], safety: bool = False) -> str:
        """
        Execute data mining commands.
        
        Commands:
        - scrape <url> [selector] - Scrape web page content
        - extract <url> <pattern> - Extract data using regex pattern
        - crawl <url> [depth] - Crawl website with specified depth
        - api <url> [params] - Make API request and extract data
        - table <url> - Extract tables from web page
        - links <url> - Extract all links from page
        - images <url> - Extract image URLs from page
        - text <url> - Extract clean text content
        - monitor <url> <interval> - Monitor page for changes
        - batch <file> - Process batch of URLs from file
        - analyze <data_file> - Analyze extracted data patterns
        - export <data> <format> - Export data to various formats
        """
        if not blocks:
            return self._get_help()
        
        command_line = blocks[0].strip()
        parts = command_line.split()
        
        if len(parts) < 1:
            return self._get_help()
        
        command = parts[0].lower()
        
        try:
            if command == "scrape":
                if len(parts) < 2:
                    return "Error: Please specify a URL to scrape"
                url = parts[1]
                selector = parts[2] if len(parts) > 2 else None
                return self._scrape_page(url, selector)
            
            elif command == "extract":
                if len(parts) < 3:
                    return "Error: Please specify URL and extraction pattern"
                url = parts[1]
                pattern = ' '.join(parts[2:])
                return self._extract_pattern(url, pattern)
            
            elif command == "crawl":
                if len(parts) < 2:
                    return "Error: Please specify a URL to crawl"
                url = parts[1]
                depth = int(parts[2]) if len(parts) > 2 and parts[2].isdigit() else 2
                return self._crawl_website(url, depth)
            
            elif command == "api":
                if len(parts) < 2:
                    return "Error: Please specify an API URL"
                url = parts[1]
                params = ' '.join(parts[2:]) if len(parts) > 2 else None
                return self._api_request(url, params)
            
            elif command == "table":
                if len(parts) < 2:
                    return "Error: Please specify a URL to extract tables from"
                return self._extract_tables(parts[1])
            
            elif command == "links":
                if len(parts) < 2:
                    return "Error: Please specify a URL to extract links from"
                return self._extract_links(parts[1])
            
            elif command == "images":
                if len(parts) < 2:
                    return "Error: Please specify a URL to extract images from"
                return self._extract_images(parts[1])
            
            elif command == "text":
                if len(parts) < 2:
                    return "Error: Please specify a URL to extract text from"
                return self._extract_text(parts[1])
            
            elif command == "monitor":
                if len(parts) < 3:
                    return "Error: Please specify URL and monitoring interval"
                url = parts[1]
                interval = int(parts[2]) if parts[2].isdigit() else 60
                return self._monitor_page(url, interval)
            
            elif command == "batch":
                if len(parts) < 2:
                    return "Error: Please specify a file containing URLs"
                return self._batch_process(parts[1])
            
            elif command == "analyze":
                if len(parts) < 2:
                    return "Error: Please specify a data file to analyze"
                return self._analyze_data(parts[1])
            
            elif command == "export":
                if len(parts) < 3:
                    return "Error: Please specify data source and export format"
                data_source = parts[1]
                export_format = parts[2]
                return self._export_data(data_source, export_format)
            
            else:
                return f"Unknown command: {command}\n\n{self._get_help()}"
        
        except Exception as e:
            return f"Error during data mining: {str(e)}"
    
    def _get_help(self) -> str:
        """Return help information for the data miner."""
        available_deps = [dep for dep, available in self.dependencies.items() if available]
        missing_deps = [dep for dep, available in self.dependencies.items() if not available]
        
        return f"""Data Miner Commands:

scrape <url> [selector]        - Scrape web page content
extract <url> <pattern>        - Extract data using regex pattern
crawl <url> [depth]            - Crawl website (default depth: 2)
api <url> [params]             - Make API request and extract data
table <url>                    - Extract tables from web page
links <url>                    - Extract all links from page
images <url>                   - Extract image URLs from page
text <url>                     - Extract clean text content
monitor <url> <interval>       - Monitor page for changes (seconds)
batch <file>                   - Process batch of URLs from file
analyze <data_file>            - Analyze extracted data patterns
export <data> <format>         - Export data (csv, json, xml)

Available Dependencies: {', '.join(available_deps) if available_deps else 'None'}
Missing Dependencies: {', '.join(missing_deps) if missing_deps else 'None'}

To install missing dependencies:
pip install beautifulsoup4 pandas selenium scrapy lxml

Examples:
```data_miner
scrape https://example.com .content
```

```data_miner
extract https://news.com "\\d{{4}}-\\d{{2}}-\\d{{2}}"
```

```data_miner
table https://example.com/data
```

Note: Be respectful of robots.txt and rate limits when scraping.
"""
    
    def _rate_limit(self):
        """Implement rate limiting for requests."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.min_request_interval:
            time.sleep(self.min_request_interval - time_since_last)
        self.last_request_time = time.time()
    
    def _scrape_page(self, url: str, selector: Optional[str] = None) -> str:
        """Scrape web page content."""
        self._rate_limit()
        
        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            result = f"=== WEB SCRAPING RESULTS ===\n\nURL: {url}\n"
            result += f"Status Code: {response.status_code}\n"
            result += f"Content Type: {response.headers.get('content-type', 'Unknown')}\n"
            result += f"Page Size: {len(response.content):,} bytes\n\n"
            
            if selector:
                # Extract specific elements using CSS selector
                elements = soup.select(selector)
                result += f"=== SELECTED CONTENT ({selector}) ===\n"
                result += f"Found {len(elements)} elements\n\n"
                
                for i, element in enumerate(elements[:10], 1):  # Limit to first 10
                    result += f"{i}. {element.get_text(strip=True)}\n"
            else:
                # Extract general page information
                title = soup.find('title')
                result += f"Title: {title.get_text(strip=True) if title else 'No title'}\n"
                
                # Meta description
                meta_desc = soup.find('meta', attrs={'name': 'description'})
                if meta_desc:
                    result += f"Description: {meta_desc.get('content', '')}\n"
                
                # Headings
                headings = soup.find_all(['h1', 'h2', 'h3'])
                if headings:
                    result += f"\n=== HEADINGS ===\n"
                    for heading in headings[:10]:
                        result += f"{heading.name.upper()}: {heading.get_text(strip=True)}\n"
                
                # Paragraphs
                paragraphs = soup.find_all('p')
                if paragraphs:
                    result += f"\n=== CONTENT PREVIEW ===\n"
                    for p in paragraphs[:5]:
                        text = p.get_text(strip=True)
                        if len(text) > 50:
                            result += f"{text[:200]}...\n\n"
            
            return result
        
        except Exception as e:
            return f"Error scraping page: {str(e)}"
    
    def _extract_pattern(self, url: str, pattern: str) -> str:
        """Extract data using regex pattern."""
        self._rate_limit()
        
        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            # Apply regex pattern
            matches = re.findall(pattern, response.text, re.MULTILINE | re.IGNORECASE)
            
            result = f"=== PATTERN EXTRACTION RESULTS ===\n\n"
            result += f"URL: {url}\n"
            result += f"Pattern: {pattern}\n"
            result += f"Matches Found: {len(matches)}\n\n"
            
            if matches:
                result += "=== EXTRACTED DATA ===\n"
                for i, match in enumerate(matches[:50], 1):  # Limit to first 50
                    if isinstance(match, tuple):
                        result += f"{i}. {' | '.join(match)}\n"
                    else:
                        result += f"{i}. {match}\n"
            else:
                result += "No matches found for the specified pattern.\n"
            
            return result
        
        except Exception as e:
            return f"Error extracting pattern: {str(e)}"
    
    def _crawl_website(self, url: str, depth: int) -> str:
        """Crawl website with specified depth."""
        visited = set()
        to_visit = [(url, 0)]
        results = []
        
        result = f"=== WEBSITE CRAWLING ===\n\nStarting URL: {url}\nMax Depth: {depth}\n\n"
        
        while to_visit and len(visited) < 50:  # Limit to 50 pages
            current_url, current_depth = to_visit.pop(0)
            
            if current_url in visited or current_depth > depth:
                continue
            
            visited.add(current_url)
            self._rate_limit()
            
            try:
                response = self.session.get(current_url, timeout=15)
                response.raise_for_status()
                
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Extract page info
                title = soup.find('title')
                page_info = {
                    'url': current_url,
                    'title': title.get_text(strip=True) if title else 'No title',
                    'status': response.status_code,
                    'size': len(response.content),
                    'depth': current_depth
                }
                results.append(page_info)
                
                # Find links for next level
                if current_depth < depth:
                    links = soup.find_all('a', href=True)
                    for link in links:
                        href = link['href']
                        absolute_url = urljoin(current_url, href)
                        
                        # Only crawl same domain
                        if urlparse(absolute_url).netloc == urlparse(url).netloc:
                            if absolute_url not in visited:
                                to_visit.append((absolute_url, current_depth + 1))
            
            except Exception as e:
                results.append({
                    'url': current_url,
                    'title': f'Error: {str(e)}',
                    'status': 'Error',
                    'size': 0,
                    'depth': current_depth
                })
        
        # Format results
        result += f"Pages Crawled: {len(results)}\n\n"
        for i, page in enumerate(results, 1):
            result += f"{i}. {page['title']}\n"
            result += f"   URL: {page['url']}\n"
            result += f"   Status: {page['status']} | Size: {page['size']:,} bytes | Depth: {page['depth']}\n\n"
        
        return result
    
    def _api_request(self, url: str, params: Optional[str] = None) -> str:
        """Make API request and extract data."""
        self._rate_limit()
        
        try:
            # Parse parameters if provided
            request_params = {}
            if params:
                # Simple parameter parsing (key=value&key2=value2)
                for param in params.split('&'):
                    if '=' in param:
                        key, value = param.split('=', 1)
                        request_params[key] = value
            
            response = self.session.get(url, params=request_params, timeout=30)
            response.raise_for_status()
            
            result = f"=== API REQUEST RESULTS ===\n\n"
            result += f"URL: {url}\n"
            result += f"Parameters: {request_params}\n"
            result += f"Status Code: {response.status_code}\n"
            result += f"Content Type: {response.headers.get('content-type', 'Unknown')}\n\n"
            
            # Try to parse as JSON
            try:
                data = response.json()
                result += "=== JSON RESPONSE ===\n"
                result += json.dumps(data, indent=2)[:2000]  # Limit output
                if len(json.dumps(data)) > 2000:
                    result += "\n... (truncated)"
            except json.JSONDecodeError:
                # Fallback to text content
                result += "=== TEXT RESPONSE ===\n"
                result += response.text[:2000]
                if len(response.text) > 2000:
                    result += "\n... (truncated)"
            
            return result
        
        except Exception as e:
            return f"Error making API request: {str(e)}"
    
    def _extract_tables(self, url: str) -> str:
        """Extract tables from web page."""
        if not self.dependencies['pandas']:
            return "Error: pandas is required for table extraction. Install with: pip install pandas"
        
        try:
            import pandas as pd
            
            # Use pandas to read HTML tables
            tables = pd.read_html(url)
            
            result = f"=== TABLE EXTRACTION RESULTS ===\n\n"
            result += f"URL: {url}\n"
            result += f"Tables Found: {len(tables)}\n\n"
            
            for i, table in enumerate(tables, 1):
                result += f"=== TABLE {i} ===\n"
                result += f"Dimensions: {table.shape[0]} rows × {table.shape[1]} columns\n"
                result += f"Columns: {', '.join(table.columns.astype(str))}\n\n"
                
                # Show first few rows
                result += "First 5 rows:\n"
                result += table.head().to_string()
                result += "\n\n"
            
            return result
        
        except Exception as e:
            return f"Error extracting tables: {str(e)}"
    
    def _extract_links(self, url: str) -> str:
        """Extract all links from page."""
        self._rate_limit()
        
        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            links = soup.find_all('a', href=True)
            
            result = f"=== LINK EXTRACTION RESULTS ===\n\n"
            result += f"URL: {url}\n"
            result += f"Links Found: {len(links)}\n\n"
            
            # Categorize links
            internal_links = []
            external_links = []
            
            base_domain = urlparse(url).netloc
            
            for link in links:
                href = link['href']
                absolute_url = urljoin(url, href)
                link_domain = urlparse(absolute_url).netloc
                
                link_info = {
                    'url': absolute_url,
                    'text': link.get_text(strip=True)[:100],
                    'title': link.get('title', '')
                }
                
                if link_domain == base_domain:
                    internal_links.append(link_info)
                else:
                    external_links.append(link_info)
            
            result += f"Internal Links: {len(internal_links)}\n"
            result += f"External Links: {len(external_links)}\n\n"
            
            # Show sample links
            result += "=== INTERNAL LINKS (Sample) ===\n"
            for i, link in enumerate(internal_links[:10], 1):
                result += f"{i}. {link['text']}\n   {link['url']}\n\n"
            
            result += "=== EXTERNAL LINKS (Sample) ===\n"
            for i, link in enumerate(external_links[:10], 1):
                result += f"{i}. {link['text']}\n   {link['url']}\n\n"
            
            return result
        
        except Exception as e:
            return f"Error extracting links: {str(e)}"
    
    def _extract_images(self, url: str) -> str:
        """Extract image URLs from page."""
        self._rate_limit()
        
        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            images = soup.find_all('img', src=True)
            
            result = f"=== IMAGE EXTRACTION RESULTS ===\n\n"
            result += f"URL: {url}\n"
            result += f"Images Found: {len(images)}\n\n"
            
            for i, img in enumerate(images, 1):
                src = img['src']
                absolute_url = urljoin(url, src)
                
                result += f"{i}. {absolute_url}\n"
                
                # Additional image info
                alt_text = img.get('alt', '')
                if alt_text:
                    result += f"   Alt: {alt_text}\n"
                
                width = img.get('width', '')
                height = img.get('height', '')
                if width and height:
                    result += f"   Size: {width}x{height}\n"
                
                result += "\n"
            
            return result
        
        except Exception as e:
            return f"Error extracting images: {str(e)}"
    
    def _extract_text(self, url: str) -> str:
        """Extract clean text content from page."""
        self._rate_limit()
        
        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Get text
            text = soup.get_text()
            
            # Clean up text
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            
            result = f"=== TEXT EXTRACTION RESULTS ===\n\n"
            result += f"URL: {url}\n"
            result += f"Text Length: {len(text):,} characters\n"
            result += f"Word Count: {len(text.split()):,} words\n\n"
            
            result += "=== EXTRACTED TEXT (Preview) ===\n"
            result += text[:2000]  # First 2000 characters
            if len(text) > 2000:
                result += "\n... (truncated)"
            
            return result
        
        except Exception as e:
            return f"Error extracting text: {str(e)}"
    
    def _monitor_page(self, url: str, interval: int) -> str:
        """Monitor page for changes (basic implementation)."""
        return f"""=== PAGE MONITORING SETUP ===

URL: {url}
Check Interval: {interval} seconds

Note: This is a basic monitoring setup. For continuous monitoring, 
you would need to implement a background process that:

1. Fetches the page content at regular intervals
2. Compares with previous version
3. Detects changes in content, structure, or specific elements
4. Alerts when changes are detected

To implement full monitoring:
- Use scheduling libraries (APScheduler, Celery)
- Store page snapshots for comparison
- Set up notification systems
- Handle rate limiting and error recovery
"""
    
    def _batch_process(self, file_path: str) -> str:
        """Process batch of URLs from file."""
        if not os.path.exists(file_path):
            return f"Error: File '{file_path}' not found"
        
        try:
            with open(file_path, 'r') as f:
                urls = [line.strip() for line in f if line.strip()]
            
            result = f"=== BATCH PROCESSING RESULTS ===\n\n"
            result += f"File: {file_path}\n"
            result += f"URLs to Process: {len(urls)}\n\n"
            
            for i, url in enumerate(urls[:10], 1):  # Limit to first 10
                result += f"=== URL {i}: {url} ===\n"
                try:
                    page_result = self._scrape_page(url)
                    result += page_result + "\n\n"
                except Exception as e:
                    result += f"Error processing {url}: {str(e)}\n\n"
            
            if len(urls) > 10:
                result += f"... and {len(urls) - 10} more URLs (truncated for display)\n"
            
            return result
        
        except Exception as e:
            return f"Error processing batch file: {str(e)}"
    
    def _analyze_data(self, data_file: str) -> str:
        """Analyze extracted data patterns."""
        if not os.path.exists(data_file):
            return f"Error: Data file '{data_file}' not found"
        
        try:
            # Try to read as CSV first
            if data_file.endswith('.csv'):
                if self.dependencies['pandas']:
                    import pandas as pd
                    df = pd.read_csv(data_file)
                    
                    result = f"=== DATA ANALYSIS RESULTS ===\n\n"
                    result += f"File: {data_file}\n"
                    result += f"Dimensions: {df.shape[0]} rows × {df.shape[1]} columns\n"
                    result += f"Columns: {', '.join(df.columns)}\n\n"
                    
                    result += "=== DATA SUMMARY ===\n"
                    result += str(df.describe(include='all'))
                    
                    return result
                else:
                    return "Error: pandas required for CSV analysis"
            
            # Try to read as JSON
            elif data_file.endswith('.json'):
                with open(data_file, 'r') as f:
                    data = json.load(f)
                
                result = f"=== JSON DATA ANALYSIS ===\n\n"
                result += f"File: {data_file}\n"
                result += f"Data Type: {type(data).__name__}\n"
                
                if isinstance(data, list):
                    result += f"Items: {len(data)}\n"
                elif isinstance(data, dict):
                    result += f"Keys: {', '.join(data.keys())}\n"
                
                result += f"\nData Preview:\n{json.dumps(data, indent=2)[:1000]}"
                
                return result
            
            else:
                return f"Unsupported data file format: {data_file}"
        
        except Exception as e:
            return f"Error analyzing data: {str(e)}"
    
    def _export_data(self, data_source: str, export_format: str) -> str:
        """Export data to various formats."""
        return f"""=== DATA EXPORT ===

Source: {data_source}
Target Format: {export_format}

Note: Data export functionality would include:

Supported Formats:
- CSV: Comma-separated values
- JSON: JavaScript Object Notation
- XML: Extensible Markup Language
- Excel: Microsoft Excel format
- TSV: Tab-separated values

Export Process:
1. Load data from source
2. Transform to target format
3. Apply formatting and structure
4. Save to output file
5. Validate export integrity

To implement full export functionality, specify:
- Source data location and format
- Target file path and name
- Any transformation requirements
- Encoding and delimiter preferences
"""


if __name__ == "__main__":
    # Test the data miner
    miner = DataMiner()
    result = miner.execute(["scrape https://example.com"])
    print(result)
