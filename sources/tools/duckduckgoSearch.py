import requests
import json
import os
import sys
from urllib.parse import quote_plus

if __name__ == "__main__": # if running as a script for individual testing
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from sources.tools.tools import Tools

class duckduckgoSearch(Tools):
    def __init__(self):
        """
        A tool for searching using DuckDuckGo's instant answer API.
        This is a fallback when SearxNG is not available.
        """
        super().__init__()
        self.tag = "web_search"
        self.name = "duckduckgoSearch"
        self.description = "A tool for searching the web using DuckDuckGo API"
        self.user_agent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        self.paywall_keywords = [
            "Member-only", "access denied", "restricted content", "404", "this page is not working"
        ]

    def link_valid(self, link):
        """check if a link is valid."""
        if not link.startswith("http"):
            return "Status: Invalid URL"
        
        headers = {"User-Agent": self.user_agent}
        try:
            response = requests.get(link, headers=headers, timeout=5)
            status = response.status_code
            if status == 200:
                content = response.text.lower()
                if any(keyword in content for keyword in self.paywall_keywords):
                    return "Status: Possible Paywall"
                return "Status: OK"
            elif status == 404:
                return "Status: 404 Not Found"
            elif status == 403:
                return "Status: 403 Forbidden"
            else:
                return f"Status: {status} {response.reason}"
        except requests.exceptions.RequestException as e:
            return f"Error: {str(e)}"

    def execute(self, blocks: list, safety: bool = False) -> str:
        """Executes a search query using DuckDuckGo's instant answer API."""
        if not blocks:
            return "Error: No search query provided."

        query = blocks[0].strip()
        if not query:
            return "Error: Empty search query provided."

        # Use DuckDuckGo's instant answer API
        search_url = "https://api.duckduckgo.com/"
        headers = {
            'User-Agent': self.user_agent
        }
        
        params = {
            'q': query,
            'format': 'json',
            'no_html': '1',
            'skip_disambig': '1'
        }
        
        try:
            response = requests.get(search_url, headers=headers, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            results = []
            
            # Check for instant answer
            if data.get('Abstract'):
                results.append(f"Title: {data.get('Heading', 'DuckDuckGo Answer')}\nSnippet: {data['Abstract']}\nLink: {data.get('AbstractURL', 'N/A')}")
            
            # Check for related topics
            if data.get('RelatedTopics'):
                for topic in data['RelatedTopics'][:5]:  # Limit to 5 results
                    if isinstance(topic, dict) and topic.get('Text'):
                        title = topic.get('Text', '').split(' - ')[0] if ' - ' in topic.get('Text', '') else topic.get('Text', '')[:50]
                        snippet = topic.get('Text', '')
                        link = topic.get('FirstURL', 'N/A')
                        results.append(f"Title: {title}\nSnippet: {snippet}\nLink: {link}")
            
            # If no results from instant answer, try a simple web search approach
            if not results:
                # This is a basic fallback - in a real implementation you might want to use a proper search API
                results.append(f"Title: Search for '{query}'\nSnippet: No instant results available. You may want to search manually or use a browser.\nLink: https://duckduckgo.com/?q={quote_plus(query)}")
            
            if len(results) == 0:
                return "No search results found."
            
            return "\n\n".join(results)
            
        except requests.exceptions.RequestException as e:
            return f"Error: DuckDuckGo search failed: {str(e)}"
        except Exception as e:
            return f"Error: Unexpected error during search: {str(e)}"

    def execution_failure_check(self, output: str) -> bool:
        """
        Checks if the execution failed based on the output.
        """
        return "Error" in output

    def interpreter_feedback(self, output: str) -> str:
        """
        Feedback of web search to agent.
        """
        if self.execution_failure_check(output):
            return f"Web search failed: {output}"
        return f"Web search result:\n{output}"

if __name__ == "__main__":
    search_tool = duckduckgoSearch()
    result = search_tool.execute(["what is artificial intelligence?"])
    print(result)
