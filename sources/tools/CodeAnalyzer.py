import os
import sys
import ast
import re
import subprocess
import tempfile
from typing import Dict, List, Tuple, Optional
from pathlib import Path

if __name__ == "__main__":
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from sources.tools.tools import Tools

class CodeAnalyzer(Tools):
    """
    Advanced code analysis tool for static analysis, security scanning, 
    code review, and best practices checking.
    """
    
    def __init__(self):
        super().__init__()
        self.tag = "code_analyzer"
        self.name = "Code Analyzer"
        self.description = "Advanced code analysis, security scanning, and code review tool"
        self.supported_languages = {
            'python': ['.py'],
            'javascript': ['.js', '.jsx', '.ts', '.tsx'],
            'java': ['.java'],
            'c': ['.c', '.h'],
            'cpp': ['.cpp', '.cxx', '.cc', '.hpp'],
            'go': ['.go'],
            'rust': ['.rs'],
            'php': ['.php'],
            'ruby': ['.rb'],
            'shell': ['.sh', '.bash']
        }
    
    def execute(self, blocks: List[str], safety: bool = False) -> str:
        """
        Execute code analysis based on the command provided.
        
        Commands:
        - analyze <file_path> - Analyze a specific file
        - security_scan <file_path> - Security vulnerability scan
        - complexity <file_path> - Calculate code complexity
        - review <file_path> - Comprehensive code review
        - patterns <file_path> - Check design patterns and best practices
        - dependencies <file_path> - Analyze dependencies and imports
        """
        if not blocks:
            return self._get_help()
        
        command_line = blocks[0].strip()
        parts = command_line.split()
        
        if len(parts) < 1:
            return self._get_help()
        
        command = parts[0].lower()
        
        try:
            if command == "analyze":
                if len(parts) < 2:
                    return "Error: Please specify a file path to analyze"
                return self._analyze_file(parts[1])
            
            elif command == "security_scan":
                if len(parts) < 2:
                    return "Error: Please specify a file path for security scan"
                return self._security_scan(parts[1])
            
            elif command == "complexity":
                if len(parts) < 2:
                    return "Error: Please specify a file path for complexity analysis"
                return self._complexity_analysis(parts[1])
            
            elif command == "review":
                if len(parts) < 2:
                    return "Error: Please specify a file path for code review"
                return self._code_review(parts[1])
            
            elif command == "patterns":
                if len(parts) < 2:
                    return "Error: Please specify a file path for pattern analysis"
                return self._pattern_analysis(parts[1])
            
            elif command == "dependencies":
                if len(parts) < 2:
                    return "Error: Please specify a file path for dependency analysis"
                return self._dependency_analysis(parts[1])
            
            else:
                return f"Unknown command: {command}\n\n{self._get_help()}"
        
        except Exception as e:
            return f"Error during code analysis: {str(e)}"
    
    def _get_help(self) -> str:
        """Return help information for the code analyzer."""
        return """Code Analyzer Commands:

analyze <file_path>        - General code analysis
security_scan <file_path>  - Security vulnerability scan
complexity <file_path>     - Calculate code complexity metrics
review <file_path>         - Comprehensive code review
patterns <file_path>       - Check design patterns and best practices
dependencies <file_path>   - Analyze dependencies and imports

Examples:
```code_analyzer
analyze src/main.py
```

```code_analyzer
security_scan app.js
```

```code_analyzer
review MyClass.java
```
"""
    
    def _get_language(self, file_path: str) -> Optional[str]:
        """Detect programming language from file extension."""
        ext = Path(file_path).suffix.lower()
        for lang, extensions in self.supported_languages.items():
            if ext in extensions:
                return lang
        return None
    
    def _analyze_file(self, file_path: str) -> str:
        """Perform general code analysis."""
        if not os.path.exists(file_path):
            return f"Error: File '{file_path}' not found"
        
        language = self._get_language(file_path)
        if not language:
            return f"Error: Unsupported file type for '{file_path}'"
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            return f"Error reading file: {str(e)}"
        
        analysis = {
            'file': file_path,
            'language': language,
            'lines_of_code': len(content.splitlines()),
            'file_size': len(content),
            'functions': self._count_functions(content, language),
            'classes': self._count_classes(content, language),
            'comments': self._analyze_comments(content, language),
            'imports': self._analyze_imports(content, language)
        }
        
        return self._format_analysis_result(analysis)
    
    def _security_scan(self, file_path: str) -> str:
        """Perform security vulnerability scanning."""
        if not os.path.exists(file_path):
            return f"Error: File '{file_path}' not found"
        
        language = self._get_language(file_path)
        if not language:
            return f"Error: Unsupported file type for '{file_path}'"
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            return f"Error reading file: {str(e)}"
        
        vulnerabilities = []
        
        # Common security patterns to check
        security_patterns = {
            'sql_injection': [
                r'execute\s*\(\s*["\'].*\+.*["\']',
                r'query\s*\(\s*["\'].*\+.*["\']',
                r'SELECT.*\+.*FROM',
                r'INSERT.*\+.*INTO'
            ],
            'xss': [
                r'innerHTML\s*=.*\+',
                r'document\.write\s*\(.*\+',
                r'eval\s*\(',
                r'setTimeout\s*\(\s*["\'].*\+.*["\']'
            ],
            'hardcoded_secrets': [
                r'password\s*=\s*["\'][^"\']+["\']',
                r'api_key\s*=\s*["\'][^"\']+["\']',
                r'secret\s*=\s*["\'][^"\']+["\']',
                r'token\s*=\s*["\'][^"\']+["\']'
            ],
            'unsafe_functions': [
                r'exec\s*\(',
                r'eval\s*\(',
                r'system\s*\(',
                r'shell_exec\s*\(',
                r'os\.system\s*\('
            ]
        }
        
        lines = content.splitlines()
        for i, line in enumerate(lines, 1):
            for vuln_type, patterns in security_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, line, re.IGNORECASE):
                        vulnerabilities.append({
                            'type': vuln_type,
                            'line': i,
                            'code': line.strip(),
                            'severity': self._get_severity(vuln_type)
                        })
        
        return self._format_security_result(file_path, vulnerabilities)
    
    def _complexity_analysis(self, file_path: str) -> str:
        """Calculate code complexity metrics."""
        if not os.path.exists(file_path):
            return f"Error: File '{file_path}' not found"
        
        language = self._get_language(file_path)
        if language != 'python':
            return "Complexity analysis currently only supports Python files"
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            complexity_analyzer = ComplexityVisitor()
            complexity_analyzer.visit(tree)
            
            return self._format_complexity_result(file_path, complexity_analyzer.get_results())
        
        except SyntaxError as e:
            return f"Syntax error in file: {str(e)}"
        except Exception as e:
            return f"Error analyzing complexity: {str(e)}"
    
    def _code_review(self, file_path: str) -> str:
        """Perform comprehensive code review."""
        analysis = self._analyze_file(file_path)
        security = self._security_scan(file_path)
        
        # Additional review checks
        review_items = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            language = self._get_language(file_path)
            
            # Check for code smells
            review_items.extend(self._check_code_smells(content, language))
            
            # Check naming conventions
            review_items.extend(self._check_naming_conventions(content, language))
            
            # Check documentation
            review_items.extend(self._check_documentation(content, language))
            
        except Exception as e:
            return f"Error during code review: {str(e)}"
        
        return f"""=== COMPREHENSIVE CODE REVIEW ===

{analysis}

{security}

=== CODE REVIEW FINDINGS ===
{self._format_review_items(review_items)}
"""
    
    def _pattern_analysis(self, file_path: str) -> str:
        """Analyze design patterns and best practices."""
        if not os.path.exists(file_path):
            return f"Error: File '{file_path}' not found"
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            return f"Error reading file: {str(e)}"
        
        language = self._get_language(file_path)
        patterns = []
        
        # Design pattern detection
        pattern_signatures = {
            'singleton': [r'class.*:\s*\n.*_instance\s*=\s*None', r'def __new__\('],
            'factory': [r'def create_.*\(', r'class.*Factory'],
            'observer': [r'def notify\(', r'def subscribe\(', r'def unsubscribe\('],
            'decorator': [r'@\w+', r'def wrapper\('],
            'strategy': [r'class.*Strategy', r'def execute\(.*strategy'],
            'builder': [r'class.*Builder', r'def build\(']
        }
        
        for pattern_name, signatures in pattern_signatures.items():
            for signature in signatures:
                if re.search(signature, content, re.MULTILINE):
                    patterns.append(pattern_name)
                    break
        
        # Best practices check
        best_practices = self._check_best_practices(content, language)
        
        return self._format_pattern_result(file_path, patterns, best_practices)
    
    def _dependency_analysis(self, file_path: str) -> str:
        """Analyze dependencies and imports."""
        if not os.path.exists(file_path):
            return f"Error: File '{file_path}' not found"
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            return f"Error reading file: {str(e)}"
        
        language = self._get_language(file_path)
        dependencies = self._extract_dependencies(content, language)
        
        return self._format_dependency_result(file_path, dependencies)
    
    # Helper methods for analysis
    def _count_functions(self, content: str, language: str) -> int:
        """Count functions in the code."""
        if language == 'python':
            return len(re.findall(r'^\s*def\s+\w+', content, re.MULTILINE))
        elif language in ['javascript', 'typescript']:
            return len(re.findall(r'function\s+\w+|=>\s*{|\w+\s*:\s*function', content))
        elif language == 'java':
            return len(re.findall(r'(public|private|protected)?\s*(static)?\s*\w+\s+\w+\s*\(', content))
        return 0
    
    def _count_classes(self, content: str, language: str) -> int:
        """Count classes in the code."""
        if language == 'python':
            return len(re.findall(r'^\s*class\s+\w+', content, re.MULTILINE))
        elif language == 'java':
            return len(re.findall(r'(public|private)?\s*class\s+\w+', content))
        elif language in ['javascript', 'typescript']:
            return len(re.findall(r'class\s+\w+', content))
        return 0
    
    def _analyze_comments(self, content: str, language: str) -> Dict:
        """Analyze comments in the code."""
        lines = content.splitlines()
        total_lines = len(lines)
        comment_lines = 0
        
        comment_patterns = {
            'python': [r'^\s*#', r'^\s*"""', r'^\s*\'\'\''],
            'javascript': [r'^\s*//', r'^\s*/\*', r'^\s*\*'],
            'java': [r'^\s*//', r'^\s*/\*', r'^\s*\*'],
            'c': [r'^\s*//', r'^\s*/\*', r'^\s*\*'],
            'cpp': [r'^\s*//', r'^\s*/\*', r'^\s*\*']
        }
        
        patterns = comment_patterns.get(language, [])
        for line in lines:
            for pattern in patterns:
                if re.match(pattern, line):
                    comment_lines += 1
                    break
        
        return {
            'total_lines': total_lines,
            'comment_lines': comment_lines,
            'comment_ratio': comment_lines / total_lines if total_lines > 0 else 0
        }
    
    def _analyze_imports(self, content: str, language: str) -> List[str]:
        """Extract imports/includes from the code."""
        imports = []
        
        if language == 'python':
            imports.extend(re.findall(r'^\s*import\s+(\w+)', content, re.MULTILINE))
            imports.extend(re.findall(r'^\s*from\s+(\w+)', content, re.MULTILINE))
        elif language in ['javascript', 'typescript']:
            imports.extend(re.findall(r'import.*from\s+["\']([^"\']+)["\']', content))
            imports.extend(re.findall(r'require\s*\(\s*["\']([^"\']+)["\']', content))
        elif language in ['c', 'cpp']:
            imports.extend(re.findall(r'#include\s*[<"]([^>"]+)[>"]', content))
        elif language == 'java':
            imports.extend(re.findall(r'import\s+([\w.]+)', content))
        
        return list(set(imports))  # Remove duplicates
    
    def _get_severity(self, vuln_type: str) -> str:
        """Get severity level for vulnerability type."""
        severity_map = {
            'sql_injection': 'HIGH',
            'xss': 'HIGH',
            'hardcoded_secrets': 'MEDIUM',
            'unsafe_functions': 'MEDIUM'
        }
        return severity_map.get(vuln_type, 'LOW')
    
    def _check_code_smells(self, content: str, language: str) -> List[Dict]:
        """Check for common code smells."""
        smells = []
        lines = content.splitlines()
        
        for i, line in enumerate(lines, 1):
            # Long lines
            if len(line) > 120:
                smells.append({
                    'type': 'long_line',
                    'line': i,
                    'message': f'Line too long ({len(line)} characters)',
                    'severity': 'LOW'
                })
            
            # TODO comments
            if 'TODO' in line.upper() or 'FIXME' in line.upper():
                smells.append({
                    'type': 'todo_comment',
                    'line': i,
                    'message': 'TODO/FIXME comment found',
                    'severity': 'LOW'
                })
        
        return smells
    
    def _check_naming_conventions(self, content: str, language: str) -> List[Dict]:
        """Check naming conventions."""
        issues = []
        
        if language == 'python':
            # Check for camelCase in Python (should be snake_case)
            camel_case_vars = re.findall(r'\b[a-z]+[A-Z]\w*\b', content)
            if camel_case_vars:
                issues.append({
                    'type': 'naming_convention',
                    'message': f'camelCase variables found in Python: {", ".join(set(camel_case_vars))}',
                    'severity': 'LOW'
                })
        
        return issues
    
    def _check_documentation(self, content: str, language: str) -> List[Dict]:
        """Check documentation quality."""
        issues = []
        
        if language == 'python':
            # Check for functions without docstrings
            functions = re.findall(r'def\s+(\w+)\s*\([^)]*\):', content)
            for func in functions:
                if func.startswith('_'):  # Skip private functions
                    continue
                func_pattern = rf'def\s+{re.escape(func)}\s*\([^)]*\):\s*\n\s*"""'
                if not re.search(func_pattern, content):
                    issues.append({
                        'type': 'missing_docstring',
                        'message': f'Function "{func}" missing docstring',
                        'severity': 'LOW'
                    })
        
        return issues
    
    def _check_best_practices(self, content: str, language: str) -> List[str]:
        """Check for best practices adherence."""
        practices = []
        
        if language == 'python':
            if 'if __name__ == "__main__":' in content:
                practices.append("✓ Uses main guard")
            if re.search(r'^\s*""".*"""', content, re.MULTILINE | re.DOTALL):
                practices.append("✓ Has module docstring")
            if 'import ' in content and 'from ' in content:
                practices.append("✓ Uses imports")
        
        return practices
    
    def _extract_dependencies(self, content: str, language: str) -> Dict:
        """Extract and categorize dependencies."""
        deps = {
            'standard_library': [],
            'third_party': [],
            'local': []
        }
        
        imports = self._analyze_imports(content, language)
        
        if language == 'python':
            # Python standard library modules (partial list)
            stdlib_modules = {
                'os', 'sys', 'json', 'datetime', 'time', 'random', 'math', 
                'collections', 'itertools', 'functools', 're', 'urllib', 
                'http', 'pathlib', 'subprocess', 'threading', 'asyncio'
            }
            
            for imp in imports:
                base_module = imp.split('.')[0]
                if base_module in stdlib_modules:
                    deps['standard_library'].append(imp)
                elif '.' in imp or imp.startswith('.'):
                    deps['local'].append(imp)
                else:
                    deps['third_party'].append(imp)
        else:
            deps['third_party'] = imports
        
        return deps
    
    # Formatting methods
    def _format_analysis_result(self, analysis: Dict) -> str:
        """Format general analysis results."""
        return f"""=== CODE ANALYSIS RESULTS ===

File: {analysis['file']}
Language: {analysis['language']}
Lines of Code: {analysis['lines_of_code']}
File Size: {analysis['file_size']} bytes
Functions: {analysis['functions']}
Classes: {analysis['classes']}

=== COMMENTS ANALYSIS ===
Total Lines: {analysis['comments']['total_lines']}
Comment Lines: {analysis['comments']['comment_lines']}
Comment Ratio: {analysis['comments']['comment_ratio']:.2%}

=== IMPORTS/DEPENDENCIES ===
{', '.join(analysis['imports']) if analysis['imports'] else 'No imports found'}
"""
    
    def _format_security_result(self, file_path: str, vulnerabilities: List[Dict]) -> str:
        """Format security scan results."""
        if not vulnerabilities:
            return f"=== SECURITY SCAN RESULTS ===\n\nFile: {file_path}\nStatus: ✓ No security issues found"
        
        result = f"=== SECURITY SCAN RESULTS ===\n\nFile: {file_path}\nVulnerabilities Found: {len(vulnerabilities)}\n\n"
        
        for vuln in vulnerabilities:
            result += f"⚠️  {vuln['severity']} - {vuln['type'].replace('_', ' ').title()}\n"
            result += f"   Line {vuln['line']}: {vuln['code']}\n\n"
        
        return result
    
    def _format_complexity_result(self, file_path: str, results: Dict) -> str:
        """Format complexity analysis results."""
        return f"""=== COMPLEXITY ANALYSIS ===

File: {file_path}
Cyclomatic Complexity: {results.get('cyclomatic', 'N/A')}
Cognitive Complexity: {results.get('cognitive', 'N/A')}
Max Function Complexity: {results.get('max_function', 'N/A')}
Average Function Complexity: {results.get('avg_function', 'N/A')}
"""
    
    def _format_review_items(self, items: List[Dict]) -> str:
        """Format code review items."""
        if not items:
            return "✓ No issues found"
        
        result = ""
        for item in items:
            severity_icon = "🔴" if item['severity'] == 'HIGH' else "🟡" if item['severity'] == 'MEDIUM' else "🔵"
            result += f"{severity_icon} {item['type'].replace('_', ' ').title()}: {item['message']}\n"
            if 'line' in item:
                result += f"   Line {item['line']}\n"
            result += "\n"
        
        return result
    
    def _format_pattern_result(self, file_path: str, patterns: List[str], practices: List[str]) -> str:
        """Format pattern analysis results."""
        result = f"=== DESIGN PATTERN ANALYSIS ===\n\nFile: {file_path}\n\n"
        
        if patterns:
            result += "Design Patterns Detected:\n"
            for pattern in patterns:
                result += f"  ✓ {pattern.replace('_', ' ').title()}\n"
        else:
            result += "No common design patterns detected\n"
        
        result += "\n=== BEST PRACTICES ===\n"
        if practices:
            for practice in practices:
                result += f"  {practice}\n"
        else:
            result += "  No best practices detected\n"
        
        return result
    
    def _format_dependency_result(self, file_path: str, dependencies: Dict) -> str:
        """Format dependency analysis results."""
        result = f"=== DEPENDENCY ANALYSIS ===\n\nFile: {file_path}\n\n"
        
        for dep_type, deps in dependencies.items():
            if deps:
                result += f"{dep_type.replace('_', ' ').title()}:\n"
                for dep in deps:
                    result += f"  - {dep}\n"
                result += "\n"
        
        return result


class ComplexityVisitor(ast.NodeVisitor):
    """AST visitor for calculating code complexity metrics."""
    
    def __init__(self):
        self.complexity = 0
        self.functions = []
        self.current_function = None
        self.current_complexity = 0
    
    def visit_FunctionDef(self, node):
        old_function = self.current_function
        old_complexity = self.current_complexity
        
        self.current_function = node.name
        self.current_complexity = 1  # Base complexity
        
        self.generic_visit(node)
        
        self.functions.append({
            'name': self.current_function,
            'complexity': self.current_complexity
        })
        
        self.current_function = old_function
        self.current_complexity = old_complexity
    
    def visit_If(self, node):
        self.current_complexity += 1
        self.generic_visit(node)
    
    def visit_While(self, node):
        self.current_complexity += 1
        self.generic_visit(node)
    
    def visit_For(self, node):
        self.current_complexity += 1
        self.generic_visit(node)
    
    def visit_ExceptHandler(self, node):
        self.current_complexity += 1
        self.generic_visit(node)
    
    def visit_With(self, node):
        self.current_complexity += 1
        self.generic_visit(node)
    
    def get_results(self) -> Dict:
        """Get complexity analysis results."""
        if not self.functions:
            return {'cyclomatic': 0, 'cognitive': 0, 'max_function': 0, 'avg_function': 0}
        
        complexities = [f['complexity'] for f in self.functions]
        return {
            'cyclomatic': sum(complexities),
            'cognitive': sum(complexities),  # Simplified
            'max_function': max(complexities),
            'avg_function': sum(complexities) / len(complexities)
        }


if __name__ == "__main__":
    # Test the code analyzer
    analyzer = CodeAnalyzer()
    result = analyzer.execute(["analyze test.py"])
    print(result)
