import platform
import os
import asyncio

from sources.utility import pretty_print, animate_thinking
from sources.agents.agent import Agent, executorR<PERSON>ult
from sources.tools.C_Interpreter import CInterpreter
from sources.tools.GoInterpreter import GoInterpreter
from sources.tools.PyInterpreter import Py<PERSON>nterpreter
from sources.tools.BashInterpreter import BashInterpreter
from sources.tools.JavaInterpreter import JavaInterpreter
from sources.tools.fileFinder import FileFinder
from sources.tools.CodeAnalyzer import CodeAnalyzer
from sources.tools.PackageManager import PackageManager
from sources.tools.TestGenerator import TestGenerator
from sources.logger import Logger
from sources.memory import Memory

class ExpertCoderAgent(Agent):
    """
    The Expert Coder Agent is an advanced coding agent with expert-level capabilities including:
    - Advanced code analysis and security scanning
    - Multi-language package management
    - Automated test generation
    - Code review and best practices
    - Design pattern implementation
    - Performance optimization
    """
    
    def __init__(self, name, prompt_path, provider, verbose=False):
        super().__init__(name, prompt_path, provider, verbose, None)
        self.tools = {
            "bash": Ba<PERSON><PERSON>nterpreter(),
            "python": <PERSON><PERSON><PERSON><PERSON>p<PERSON><PERSON>(),
            "c": <PERSON><PERSON><PERSON>p<PERSON><PERSON>(),
            "go": GoInterpreter(),
            "java": JavaInterpreter(),
            "file_finder": FileFinder(),
            "code_analyzer": CodeAnalyzer(),
            "package_manager": PackageManager(),
            "test_generator": TestGenerator()
        }
        self.work_dir = self.tools["file_finder"].get_work_dir()
        self.role = "expert_code"
        self.type = "expert_coder_agent"
        self.logger = Logger("expert_coder_agent.log")
        self.memory = Memory(self.load_prompt(prompt_path),
                        recover_last_session=False,
                        memory_compression=False,
                        model_provider=provider.get_model_name())
    
    async def process(self, prompt, speech_module) -> str:
        """
        Process the prompt with expert coding capabilities.
        Enhanced with advanced code analysis, package management, and test generation.
        """
        exec_success = False
        
        # Add expert coding context to the prompt
        enhanced_prompt = self._enhance_prompt_with_context(prompt)
        enhanced_prompt += f"\nYou must work in directory: {self.work_dir}"
        
        self.memory.push('user', enhanced_prompt)
        
        while exec_success is False and not self.stop:
            await self.wait_message(speech_module)
            animate_thinking("Analyzing and coding...", color="status")
            
            answer, reasoning = await self.llm_request()
            self.last_reasoning = reasoning
            
            # Enhanced execution with expert tools
            exec_success, feedback = self.execute_modules(answer)
            
            # Post-execution analysis and suggestions
            if exec_success:
                suggestions = self._generate_expert_suggestions(answer, feedback)
                if suggestions:
                    answer += f"\n\n🎯 **Expert Suggestions:**\n{suggestions}"
            
            answer = self.remove_blocks(answer)
            self.last_answer = answer
        
        self.status_message = "Ready"
        return answer, reasoning
    
    def _enhance_prompt_with_context(self, prompt: str) -> str:
        """
        Enhance the prompt with expert coding context and capabilities.
        """
        expert_context = """
🚀 **EXPERT CODING MODE ACTIVATED**

You now have access to advanced coding tools:

**Code Analysis & Review:**
- `code_analyzer analyze <file>` - Comprehensive code analysis
- `code_analyzer security_scan <file>` - Security vulnerability scanning
- `code_analyzer complexity <file>` - Code complexity metrics
- `code_analyzer review <file>` - Full code review with best practices
- `code_analyzer patterns <file>` - Design pattern analysis

**Package Management:**
- `package_manager install <lang> <pm> <package>` - Install packages
- `package_manager detect .` - Auto-detect project type
- `package_manager audit <lang> <pm>` - Security audit dependencies

**Test Generation:**
- `test_generator generate <file> [framework]` - Generate comprehensive tests
- `test_generator unit <file>` - Generate unit tests
- `test_generator mock <file>` - Generate tests with mocking

**Expert Guidelines:**
1. Always analyze code for security vulnerabilities
2. Follow language-specific best practices and conventions
3. Generate tests for critical functions
4. Use appropriate design patterns
5. Optimize for performance and maintainability
6. Provide comprehensive documentation

"""
        return expert_context + "\n" + prompt
    
    def _generate_expert_suggestions(self, answer: str, feedback: str) -> str:
        """
        Generate expert-level suggestions based on the executed code.
        """
        suggestions = []
        
        # Check if code was written
        if any(tag in answer for tag in ['```python', '```java', '```go', '```c', '```javascript']):
            suggestions.append("💡 Consider running `code_analyzer review <filename>` to get expert code review")
            suggestions.append("🧪 Generate tests with `test_generator generate <filename>` for better code quality")
        
        # Check if packages were mentioned
        if any(word in answer.lower() for word in ['import', 'require', 'include', 'using']):
            suggestions.append("📦 Use `package_manager detect .` to analyze project dependencies")
            suggestions.append("🔒 Run `package_manager audit <lang> <pm>` to check for security vulnerabilities")
        
        # Check for potential security issues
        security_keywords = ['password', 'api_key', 'secret', 'token', 'sql', 'query', 'execute']
        if any(keyword in answer.lower() for keyword in security_keywords):
            suggestions.append("⚠️  Security Alert: Run `code_analyzer security_scan <filename>` to check for vulnerabilities")
        
        # Performance suggestions
        if any(word in answer.lower() for word in ['loop', 'iteration', 'recursive', 'algorithm']):
            suggestions.append("⚡ Performance: Consider analyzing complexity with `code_analyzer complexity <filename>`")
        
        return "\n".join(f"  {suggestion}" for suggestion in suggestions)
    
    def execute_modules(self, answer: str) -> tuple:
        """
        Enhanced module execution with expert-level error handling and feedback.
        """
        self.blocks_result = []
        self.success = True
        
        for name, tool in self.tools.items():
            feedback = ""
            blocks, save_path = tool.load_exec_block(answer)
            
            if blocks is not None:
                pretty_print(f"🔧 Executing {len(blocks)} {name} blocks...", color="status")
                
                for block in blocks:
                    self.show_block(block)
                    
                    # Enhanced execution with expert context
                    if name in ['code_analyzer', 'package_manager', 'test_generator']:
                        pretty_print(f"🎯 Running expert tool: {name}", color="info")
                    
                    output = tool.execute([block])
                    feedback = tool.interpreter_feedback(output)
                    success = not tool.execution_failure_check(output)
                    
                    # Enhanced feedback for expert tools
                    if name in ['code_analyzer', 'package_manager', 'test_generator'] and success:
                        feedback = self._enhance_tool_feedback(name, output, feedback)
                    
                    self.blocks_result.append(executorResult(block, feedback, success, name))
                    
                    if not success:
                        self.success = False
                        self.memory.push('user', feedback)
                        return False, feedback
                
                self.memory.push('user', feedback)
                
                if save_path is not None:
                    tool.save_block(blocks, save_path)
                    pretty_print(f"💾 Saved to: {save_path}", color="success")
        
        return True, feedback
    
    def _enhance_tool_feedback(self, tool_name: str, output: str, original_feedback: str) -> str:
        """
        Enhance feedback from expert tools with additional context and suggestions.
        """
        enhanced_feedback = original_feedback
        
        if tool_name == 'code_analyzer':
            if 'security' in output.lower() and 'vulnerability' in output.lower():
                enhanced_feedback += "\n\n🔒 **Security Note:** Address any HIGH or MEDIUM severity issues immediately."
            
            if 'complexity' in output.lower():
                enhanced_feedback += "\n\n📊 **Complexity Note:** Consider refactoring functions with high complexity scores."
        
        elif tool_name == 'package_manager':
            if 'successfully installed' in output.lower():
                enhanced_feedback += "\n\n📦 **Package Note:** Consider adding the new dependency to your requirements file."
            
            if 'vulnerability' in output.lower() or 'audit' in output.lower():
                enhanced_feedback += "\n\n⚠️  **Security Note:** Review and update vulnerable packages immediately."
        
        elif tool_name == 'test_generator':
            if 'generated' in output.lower():
                enhanced_feedback += "\n\n🧪 **Testing Note:** Review and customize the generated tests. Add edge cases and error scenarios."
        
        return enhanced_feedback
    
    def get_expert_capabilities(self) -> dict:
        """
        Return a summary of expert coding capabilities.
        """
        return {
            "code_analysis": {
                "security_scanning": True,
                "complexity_analysis": True,
                "code_review": True,
                "pattern_detection": True
            },
            "package_management": {
                "multi_language": True,
                "security_audit": True,
                "auto_detection": True,
                "dependency_analysis": True
            },
            "test_generation": {
                "unit_tests": True,
                "integration_tests": True,
                "mock_tests": True,
                "coverage_analysis": True
            },
            "supported_languages": [
                "Python", "JavaScript", "Java", "C", "C++", "Go", "Rust", "Ruby", "PHP", "C#"
            ],
            "frameworks": {
                "python": ["unittest", "pytest"],
                "javascript": ["jest", "mocha"],
                "java": ["junit"]
            }
        }


if __name__ == "__main__":
    pass
