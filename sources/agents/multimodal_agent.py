import platform
import os
import asyncio

from sources.utility import pretty_print, animate_thinking
from sources.agents.agent import Agent, executorR<PERSON>ult
from sources.tools.BashInterpreter import BashInterpreter
from sources.tools.fileFinder import FileFinder
from sources.tools.ImageProcessor import ImageProcessor
from sources.tools.DocumentProcessor import DocumentProcessor
from sources.logger import Logger
from sources.memory import Memory

class MultimodalAgent(Agent):
    """
    The Multimodal Agent specializes in processing and understanding various types of media:
    - Image analysis, OCR, and visual content understanding
    - Document processing (PDF, Excel, Word, PowerPoint)
    - Audio processing (if available)
    - Cross-modal content analysis and integration
    """
    
    def __init__(self, name, prompt_path, provider, verbose=False):
        super().__init__(name, prompt_path, provider, verbose, None)
        self.tools = {
            "bash": BashInterpreter(),
            "file_finder": FileFinder(),
            "image_processor": ImageProcessor(),
            "document_processor": DocumentProcessor()
        }
        self.work_dir = self.tools["file_finder"].get_work_dir()
        self.role = "multimodal"
        self.type = "multimodal_agent"
        self.logger = Logger("multimodal_agent.log")
        self.memory = Memory(self.load_prompt(prompt_path),
                        recover_last_session=False,
                        memory_compression=False,
                        model_provider=provider.get_model_name())
    
    async def process(self, prompt, speech_module) -> str:
        """
        Process the prompt with multimodal capabilities.
        Enhanced with image processing, document analysis, and cross-modal understanding.
        """
        exec_success = False
        
        # Add multimodal context to the prompt
        enhanced_prompt = self._enhance_prompt_with_context(prompt)
        enhanced_prompt += f"\nYou must work in directory: {self.work_dir}"
        
        self.memory.push('user', enhanced_prompt)
        
        while exec_success is False and not self.stop:
            await self.wait_message(speech_module)
            animate_thinking("Processing multimodal content...", color="status")
            
            answer, reasoning = await self.llm_request()
            self.last_reasoning = reasoning
            
            # Enhanced execution with multimodal tools
            exec_success, feedback = self.execute_modules(answer)
            
            # Post-execution analysis and suggestions
            if exec_success:
                suggestions = self._generate_multimodal_suggestions(answer, feedback)
                if suggestions:
                    answer += f"\n\n🎯 **Multimodal Insights:**\n{suggestions}"
            
            answer = self.remove_blocks(answer)
            self.last_answer = answer
        
        self.status_message = "Ready"
        return answer, reasoning
    
    def _enhance_prompt_with_context(self, prompt: str) -> str:
        """
        Enhance the prompt with multimodal context and capabilities.
        """
        multimodal_context = """
🎨 **MULTIMODAL PROCESSING MODE ACTIVATED**

You now have access to advanced multimodal tools:

**Image Processing:**
- `image_processor analyze <image>` - Comprehensive image analysis
- `image_processor ocr <image> [engine]` - Extract text from images
- `image_processor describe <image>` - Describe visual content
- `image_processor chart <image>` - Analyze charts and diagrams
- `image_processor screenshot <image>` - Analyze UI screenshots
- `image_processor compare <img1> <img2>` - Compare images
- `image_processor metadata <image>` - Extract image metadata

**Document Processing:**
- `document_processor analyze <doc>` - General document analysis
- `document_processor pdf <file> [pages]` - Process PDF documents
- `document_processor excel <file> [sheet]` - Process spreadsheets
- `document_processor word <file>` - Process Word documents
- `document_processor powerpoint <file>` - Process presentations
- `document_processor search <doc> <query>` - Search within documents

**Multimodal Capabilities:**
1. **Visual Understanding**: Analyze images, charts, diagrams, screenshots
2. **Document Intelligence**: Extract and understand content from various formats
3. **Cross-Modal Analysis**: Combine insights from different media types
4. **Content Extraction**: OCR, text extraction, metadata analysis
5. **Comparative Analysis**: Compare and contrast different media

**Expert Guidelines:**
- Always analyze visual content for key insights and patterns
- Extract and structure information from documents systematically
- Provide detailed descriptions of visual elements
- Identify relationships between different types of content
- Suggest improvements for visual and document accessibility

"""
        return multimodal_context + "\n" + prompt
    
    def _generate_multimodal_suggestions(self, answer: str, feedback: str) -> str:
        """
        Generate multimodal-specific suggestions based on the processed content.
        """
        suggestions = []
        
        # Check if images were processed
        if 'image_processor' in answer:
            suggestions.append("📸 Consider using OCR to extract text from images if they contain readable content")
            suggestions.append("🔍 Use `image_processor compare` to analyze differences between similar images")
            suggestions.append("📊 For charts/graphs, use `image_processor chart` for detailed analysis")
        
        # Check if documents were processed
        if 'document_processor' in answer:
            suggestions.append("📄 Use `document_processor search` to find specific information within documents")
            suggestions.append("📈 For spreadsheets, analyze data patterns and trends")
            suggestions.append("🔗 Consider extracting and cross-referencing information across multiple documents")
        
        # Check for potential multimodal workflows
        if any(ext in answer.lower() for ext in ['.pdf', '.xlsx', '.docx', '.pptx']):
            suggestions.append("🔄 Consider converting documents to different formats for better accessibility")
            suggestions.append("📋 Extract key information and create summaries or reports")
        
        if any(ext in answer.lower() for ext in ['.jpg', '.png', '.jpeg', '.gif']):
            suggestions.append("🖼️  Consider enhancing image quality if needed for better analysis")
            suggestions.append("📝 Extract metadata to understand image context and properties")
        
        # Cross-modal suggestions
        if 'image_processor' in answer and 'document_processor' in answer:
            suggestions.append("🔗 Cross-reference visual content with document information for comprehensive analysis")
            suggestions.append("📊 Create integrated reports combining visual and textual insights")
        
        return "\n".join(f"  {suggestion}" for suggestion in suggestions)
    
    def execute_modules(self, answer: str) -> tuple:
        """
        Enhanced module execution with multimodal-specific error handling and feedback.
        """
        self.blocks_result = []
        self.success = True
        
        for name, tool in self.tools.items():
            feedback = ""
            blocks, save_path = tool.load_exec_block(answer)
            
            if blocks is not None:
                pretty_print(f"🎨 Executing {len(blocks)} {name} blocks...", color="status")
                
                for block in blocks:
                    self.show_block(block)
                    
                    # Enhanced execution with multimodal context
                    if name in ['image_processor', 'document_processor']:
                        pretty_print(f"🔍 Running multimodal tool: {name}", color="info")
                    
                    output = tool.execute([block])
                    feedback = tool.interpreter_feedback(output)
                    success = not tool.execution_failure_check(output)
                    
                    # Enhanced feedback for multimodal tools
                    if name in ['image_processor', 'document_processor'] and success:
                        feedback = self._enhance_multimodal_feedback(name, output, feedback)
                    
                    self.blocks_result.append(executorResult(block, feedback, success, name))
                    
                    if not success:
                        self.success = False
                        self.memory.push('user', feedback)
                        return False, feedback
                
                self.memory.push('user', feedback)
                
                if save_path is not None:
                    tool.save_block(blocks, save_path)
                    pretty_print(f"💾 Saved to: {save_path}", color="success")
        
        return True, feedback
    
    def _enhance_multimodal_feedback(self, tool_name: str, output: str, original_feedback: str) -> str:
        """
        Enhance feedback from multimodal tools with additional context and suggestions.
        """
        enhanced_feedback = original_feedback
        
        if tool_name == 'image_processor':
            if 'ocr' in output.lower() and 'confidence' in output.lower():
                enhanced_feedback += "\n\n📝 **OCR Note:** Low confidence scores may indicate poor image quality or unclear text."
            
            if 'metadata' in output.lower():
                enhanced_feedback += "\n\n📊 **Metadata Note:** Use this information to understand image context, camera settings, and creation details."
            
            if 'chart' in output.lower() or 'diagram' in output.lower():
                enhanced_feedback += "\n\n📈 **Chart Note:** Consider extracting data points and trends for further analysis."
        
        elif tool_name == 'document_processor':
            if 'excel' in output.lower() or 'csv' in output.lower():
                enhanced_feedback += "\n\n📊 **Data Note:** Consider performing statistical analysis or creating visualizations from the data."
            
            if 'pdf' in output.lower() and 'pages' in output.lower():
                enhanced_feedback += "\n\n📄 **PDF Note:** Large documents can be processed in sections for better performance."
            
            if 'search' in output.lower() and 'matches' in output.lower():
                enhanced_feedback += "\n\n🔍 **Search Note:** Use different keywords or phrases to find additional relevant content."
        
        return enhanced_feedback
    
    def get_multimodal_capabilities(self) -> dict:
        """
        Return a summary of multimodal capabilities.
        """
        return {
            "image_processing": {
                "ocr": True,
                "analysis": True,
                "comparison": True,
                "metadata_extraction": True,
                "chart_analysis": True,
                "screenshot_analysis": True
            },
            "document_processing": {
                "pdf": True,
                "excel": True,
                "word": True,
                "powerpoint": True,
                "csv": True,
                "text_extraction": True,
                "search": True,
                "metadata": True
            },
            "supported_formats": {
                "images": [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff"],
                "documents": [".pdf", ".xlsx", ".xls", ".docx", ".pptx", ".csv", ".txt", ".md"]
            },
            "cross_modal": {
                "content_integration": True,
                "comparative_analysis": True,
                "workflow_automation": True
            }
        }
    
    def _detect_content_type(self, file_path: str) -> str:
        """
        Detect the type of content based on file extension.
        """
        from pathlib import Path
        
        ext = Path(file_path).suffix.lower()
        
        image_exts = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp']
        doc_exts = ['.pdf', '.xlsx', '.xls', '.docx', '.pptx', '.csv', '.txt', '.md']
        
        if ext in image_exts:
            return "image"
        elif ext in doc_exts:
            return "document"
        else:
            return "unknown"
    
    def _suggest_processing_workflow(self, file_paths: list) -> str:
        """
        Suggest an optimal processing workflow based on the types of files.
        """
        content_types = [self._detect_content_type(fp) for fp in file_paths]
        
        suggestions = []
        
        if "image" in content_types and "document" in content_types:
            suggestions.append("🔄 **Cross-Modal Workflow**: Process images and documents together for comprehensive analysis")
            suggestions.append("📊 **Integration**: Extract text from images and combine with document content")
        
        if content_types.count("image") > 1:
            suggestions.append("🖼️  **Image Comparison**: Use image comparison tools to identify differences and similarities")
        
        if content_types.count("document") > 1:
            suggestions.append("📄 **Document Analysis**: Search across multiple documents for common themes and information")
        
        return "\n".join(suggestions) if suggestions else "Process each file individually for best results"


if __name__ == "__main__":
    pass
