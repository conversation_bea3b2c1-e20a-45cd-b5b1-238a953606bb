import platform
import os
import asyncio

from sources.utility import pretty_print, animate_thinking
from sources.agents.agent import Agent, executorR<PERSON>ult
from sources.tools.BashInterpreter import BashInterpreter
from sources.tools.fileFinder import FileFinder
from sources.tools.AcademicSearch import AcademicSearch
from sources.tools.DataMiner import DataMiner
from sources.tools.searxSearch import searxSearch
from sources.tools.duckduckgoSearch import duckduckgoSearch
from sources.logger import Logger
from sources.memory import Memory

class ResearchAgent(Agent):
    """
    The Research Agent specializes in deep research capabilities including:
    - Academic paper search and analysis
    - Data mining and web scraping
    - Trend analysis and competitive research
    - Knowledge synthesis and report generation
    - Citation and reference management
    """
    
    def __init__(self, name, prompt_path, provider, verbose=False):
        super().__init__(name, prompt_path, provider, verbose, None)
        
        # Initialize web search tool (try SearxNG first, fallback to DuckDuckGo)
        try:
            web_search_tool = searxSearch()
        except ValueError:
            pretty_print("SearxNG not available, using DuckDuckGo search as fallback", color="warning")
            web_search_tool = duckduckgoSearch()
        
        self.tools = {
            "bash": Ba<PERSON><PERSON>nterpreter(),
            "file_finder": <PERSON><PERSON><PERSON>(),
            "academic_search": AcademicSearch(),
            "data_miner": DataMiner(),
            "web_search": web_search_tool
        }
        self.work_dir = self.tools["file_finder"].get_work_dir()
        self.role = "research"
        self.type = "research_agent"
        self.logger = Logger("research_agent.log")
        self.memory = Memory(self.load_prompt(prompt_path),
                        recover_last_session=False,
                        memory_compression=False,
                        model_provider=provider.get_model_name())
    
    async def process(self, prompt, speech_module) -> str:
        """
        Process the prompt with deep research capabilities.
        Enhanced with academic search, data mining, and comprehensive analysis.
        """
        exec_success = False
        
        # Add research context to the prompt
        enhanced_prompt = self._enhance_prompt_with_context(prompt)
        enhanced_prompt += f"\nYou must work in directory: {self.work_dir}"
        
        self.memory.push('user', enhanced_prompt)
        
        while exec_success is False and not self.stop:
            await self.wait_message(speech_module)
            animate_thinking("Conducting deep research...", color="status")
            
            answer, reasoning = await self.llm_request()
            self.last_reasoning = reasoning
            
            # Enhanced execution with research tools
            exec_success, feedback = self.execute_modules(answer)
            
            # Post-execution analysis and suggestions
            if exec_success:
                suggestions = self._generate_research_suggestions(answer, feedback)
                if suggestions:
                    answer += f"\n\n🎯 **Research Insights:**\n{suggestions}"
            
            answer = self.remove_blocks(answer)
            self.last_answer = answer
        
        self.status_message = "Ready"
        return answer, reasoning
    
    def _enhance_prompt_with_context(self, prompt: str) -> str:
        """
        Enhance the prompt with research context and capabilities.
        """
        research_context = """
🔬 **DEEP RESEARCH MODE ACTIVATED**

You now have access to advanced research tools:

**Academic Research:**
- `academic_search search <query> [source] [limit]` - Search academic papers
- `academic_search arxiv <query> [limit]` - Search arXiv papers
- `academic_search pubmed <query> [limit]` - Search PubMed articles
- `academic_search semantic <query> [limit]` - Search Semantic Scholar
- `academic_search citations <paper_id>` - Get paper citations
- `academic_search trends <topic>` - Analyze research trends
- `academic_search authors <name>` - Search by author

**Data Mining & Web Research:**
- `data_miner scrape <url> [selector]` - Scrape web content
- `data_miner extract <url> <pattern>` - Extract data patterns
- `data_miner crawl <url> [depth]` - Crawl websites
- `data_miner api <url> [params]` - Make API requests
- `data_miner table <url>` - Extract tables
- `data_miner links <url>` - Extract links
- `data_miner analyze <data_file>` - Analyze data patterns

**Web Search:**
- `web_search <query>` - General web search

**Research Capabilities:**
1. **Academic Intelligence**: Search and analyze scholarly papers
2. **Data Collection**: Systematic web scraping and data extraction
3. **Trend Analysis**: Identify patterns and emerging topics
4. **Competitive Research**: Market and technology analysis
5. **Knowledge Synthesis**: Combine insights from multiple sources
6. **Citation Management**: Track references and build bibliographies

**Research Guidelines:**
- Always verify information from multiple sources
- Cite academic sources when available
- Use systematic approaches for data collection
- Analyze trends and patterns in findings
- Provide comprehensive and well-structured reports
- Consider ethical implications of research methods

"""
        return research_context + "\n" + prompt
    
    def _generate_research_suggestions(self, answer: str, feedback: str) -> str:
        """
        Generate research-specific suggestions based on the conducted research.
        """
        suggestions = []
        
        # Check if academic search was used
        if 'academic_search' in answer:
            suggestions.append("📚 Consider cross-referencing findings with multiple academic databases")
            suggestions.append("📊 Use citation analysis to identify influential papers and authors")
            suggestions.append("🔍 Search for recent papers to ensure current information")
        
        # Check if data mining was used
        if 'data_miner' in answer:
            suggestions.append("🌐 Respect robots.txt and rate limits when scraping")
            suggestions.append("📈 Analyze extracted data for patterns and trends")
            suggestions.append("💾 Consider exporting data for further analysis")
        
        # Check if web search was used
        if 'web_search' in answer:
            suggestions.append("🔗 Verify web information with authoritative sources")
            suggestions.append("📅 Check publication dates for currency of information")
        
        # Research methodology suggestions
        if any(keyword in answer.lower() for keyword in ['research', 'study', 'analysis', 'investigation']):
            suggestions.append("📋 Create a systematic research plan with clear objectives")
            suggestions.append("📝 Document sources and methodology for reproducibility")
            suggestions.append("🔄 Consider multiple perspectives and potential biases")
        
        # Data analysis suggestions
        if any(keyword in answer.lower() for keyword in ['data', 'statistics', 'numbers', 'trends']):
            suggestions.append("📊 Visualize data to identify patterns and insights")
            suggestions.append("🧮 Apply statistical analysis for robust conclusions")
            suggestions.append("📈 Look for correlations and causal relationships")
        
        # Synthesis and reporting suggestions
        if len(answer.split('\n')) > 20:  # Long response indicating comprehensive research
            suggestions.append("📖 Create executive summary for key findings")
            suggestions.append("🔗 Build knowledge graphs to show relationships")
            suggestions.append("📚 Compile bibliography and reference list")
        
        return "\n".join(f"  {suggestion}" for suggestion in suggestions)
    
    def execute_modules(self, answer: str) -> tuple:
        """
        Enhanced module execution with research-specific error handling and feedback.
        """
        self.blocks_result = []
        self.success = True
        
        for name, tool in self.tools.items():
            feedback = ""
            blocks, save_path = tool.load_exec_block(answer)
            
            if blocks is not None:
                pretty_print(f"🔬 Executing {len(blocks)} {name} blocks...", color="status")
                
                for block in blocks:
                    self.show_block(block)
                    
                    # Enhanced execution with research context
                    if name in ['academic_search', 'data_miner']:
                        pretty_print(f"📊 Running research tool: {name}", color="info")
                    
                    output = tool.execute([block])
                    feedback = tool.interpreter_feedback(output)
                    success = not tool.execution_failure_check(output)
                    
                    # Enhanced feedback for research tools
                    if name in ['academic_search', 'data_miner', 'web_search'] and success:
                        feedback = self._enhance_research_feedback(name, output, feedback)
                    
                    self.blocks_result.append(executorResult(block, feedback, success, name))
                    
                    if not success:
                        self.success = False
                        self.memory.push('user', feedback)
                        return False, feedback
                
                self.memory.push('user', feedback)
                
                if save_path is not None:
                    tool.save_block(blocks, save_path)
                    pretty_print(f"💾 Saved to: {save_path}", color="success")
        
        return True, feedback
    
    def _enhance_research_feedback(self, tool_name: str, output: str, original_feedback: str) -> str:
        """
        Enhance feedback from research tools with additional context and suggestions.
        """
        enhanced_feedback = original_feedback
        
        if tool_name == 'academic_search':
            if 'arxiv' in output.lower():
                enhanced_feedback += "\n\n📚 **Academic Note:** arXiv papers are preprints - check for peer-reviewed versions."
            
            if 'citations' in output.lower():
                enhanced_feedback += "\n\n📊 **Citation Note:** High citation counts often indicate influential research."
            
            if 'results:' in output.lower():
                enhanced_feedback += "\n\n🔍 **Search Note:** Try different keywords or databases for comprehensive coverage."
        
        elif tool_name == 'data_miner':
            if 'scrape' in output.lower():
                enhanced_feedback += "\n\n🌐 **Scraping Note:** Always respect robots.txt and implement rate limiting."
            
            if 'table' in output.lower():
                enhanced_feedback += "\n\n📊 **Data Note:** Consider data validation and cleaning for analysis."
            
            if 'error' in output.lower():
                enhanced_feedback += "\n\n⚠️  **Error Note:** Website may have anti-scraping measures or rate limits."
        
        elif tool_name == 'web_search':
            if 'results' in output.lower():
                enhanced_feedback += "\n\n🔍 **Search Note:** Cross-reference with academic sources for verification."
        
        return enhanced_feedback
    
    def get_research_capabilities(self) -> dict:
        """
        Return a summary of research capabilities.
        """
        return {
            "academic_research": {
                "arxiv_search": True,
                "pubmed_search": True,
                "crossref_search": True,
                "semantic_scholar": True,
                "citation_analysis": True,
                "trend_analysis": True,
                "author_search": True
            },
            "data_mining": {
                "web_scraping": True,
                "pattern_extraction": True,
                "website_crawling": True,
                "api_requests": True,
                "table_extraction": True,
                "link_analysis": True,
                "text_extraction": True
            },
            "web_research": {
                "general_search": True,
                "news_search": True,
                "image_search": True,
                "real_time_results": True
            },
            "analysis_tools": {
                "data_analysis": True,
                "pattern_recognition": True,
                "trend_identification": True,
                "comparative_analysis": True,
                "statistical_analysis": True
            },
            "supported_sources": [
                "arXiv", "PubMed", "CrossRef", "Semantic Scholar",
                "Web pages", "APIs", "Data tables", "News sources"
            ]
        }
    
    def _create_research_report(self, findings: dict) -> str:
        """
        Create a structured research report from findings.
        """
        report = "=== RESEARCH REPORT ===\n\n"
        
        if 'topic' in findings:
            report += f"Research Topic: {findings['topic']}\n"
        
        if 'methodology' in findings:
            report += f"Methodology: {findings['methodology']}\n"
        
        report += f"Date: {self._get_current_date()}\n\n"
        
        if 'academic_sources' in findings:
            report += "=== ACADEMIC SOURCES ===\n"
            for source in findings['academic_sources']:
                report += f"- {source}\n"
            report += "\n"
        
        if 'web_sources' in findings:
            report += "=== WEB SOURCES ===\n"
            for source in findings['web_sources']:
                report += f"- {source}\n"
            report += "\n"
        
        if 'key_findings' in findings:
            report += "=== KEY FINDINGS ===\n"
            for finding in findings['key_findings']:
                report += f"• {finding}\n"
            report += "\n"
        
        if 'trends' in findings:
            report += "=== TRENDS IDENTIFIED ===\n"
            for trend in findings['trends']:
                report += f"→ {trend}\n"
            report += "\n"
        
        if 'recommendations' in findings:
            report += "=== RECOMMENDATIONS ===\n"
            for rec in findings['recommendations']:
                report += f"✓ {rec}\n"
            report += "\n"
        
        return report
    
    def _get_current_date(self) -> str:
        """Get current date for reports."""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d")
    
    def _suggest_research_workflow(self, topic: str) -> str:
        """
        Suggest an optimal research workflow for a given topic.
        """
        workflow = f"""=== SUGGESTED RESEARCH WORKFLOW ===

Topic: {topic}

1. **Literature Review**
   - Search academic databases (arXiv, PubMed, Semantic Scholar)
   - Identify key papers and authors
   - Analyze citation patterns

2. **Current Information Gathering**
   - Web search for recent developments
   - News and industry reports
   - Government and institutional sources

3. **Data Collection**
   - Identify relevant datasets
   - Web scraping for current data
   - API access for real-time information

4. **Analysis and Synthesis**
   - Pattern identification
   - Trend analysis
   - Comparative studies

5. **Verification and Validation**
   - Cross-reference sources
   - Fact-checking
   - Expert consultation

6. **Report Generation**
   - Executive summary
   - Detailed findings
   - Bibliography and citations
"""
        
        return workflow


if __name__ == "__main__":
    pass
